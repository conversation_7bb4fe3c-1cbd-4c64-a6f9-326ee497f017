{"name": "normalize-package-data", "version": "6.0.2", "author": "GitHub Inc.", "description": "Normalizes data that can be found in package.json files.", "license": "BSD-2-<PERSON><PERSON>", "repository": {"type": "git", "url": "git+https://github.com/npm/normalize-package-data.git"}, "main": "lib/normalize.js", "scripts": {"test": "tap", "npmclilint": "npmcli-lint", "lint": "eslint \"**/*.{js,cjs,ts,mjs,jsx,tsx}\"", "lintfix": "npm run lint -- --fix", "posttest": "npm run lint", "postsnap": "npm run lintfix --", "postlint": "template-oss-check", "snap": "tap", "template-oss-apply": "template-oss-apply --force"}, "dependencies": {"hosted-git-info": "^7.0.0", "semver": "^7.3.5", "validate-npm-package-license": "^3.0.4"}, "devDependencies": {"@npmcli/eslint-config": "^4.0.0", "@npmcli/template-oss": "4.22.0", "tap": "^16.0.1"}, "files": ["bin/", "lib/"], "engines": {"node": "^16.14.0 || >=18.0.0"}, "templateOSS": {"//@npmcli/template-oss": "This file is partially managed by @npmcli/template-oss. Edits may be overwritten.", "version": "4.22.0", "publish": "true"}, "tap": {"branches": 86, "functions": 92, "lines": 86, "statements": 86, "nyc-arg": ["--exclude", "tap-snapshots/**"]}}