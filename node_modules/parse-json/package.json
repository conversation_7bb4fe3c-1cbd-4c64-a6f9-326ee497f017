{"name": "parse-json", "version": "8.3.0", "description": "Parse JSO<PERSON> with more helpful errors", "license": "MIT", "repository": "sindresorhus/parse-json", "funding": "https://github.com/sponsors/sindresorhus", "author": {"name": "<PERSON><PERSON>", "email": "<EMAIL>", "url": "https://sindresorhus.com"}, "type": "module", "exports": {"types": "./index.d.ts", "default": "./index.js"}, "sideEffects": false, "engines": {"node": ">=18"}, "scripts": {"test": "xo && c8 ava && tsd"}, "files": ["index.js", "index.d.ts"], "keywords": ["parse", "json", "graceful", "error", "message", "humanize", "friendly", "helpful", "string"], "dependencies": {"@babel/code-frame": "^7.26.2", "index-to-position": "^1.1.0", "type-fest": "^4.39.1"}, "devDependencies": {"ava": "^6.2.0", "c8": "^10.1.3", "outdent": "^0.8.0", "strip-ansi": "^7.1.0", "tsd": "^0.31.2", "xo": "^0.60.0"}}