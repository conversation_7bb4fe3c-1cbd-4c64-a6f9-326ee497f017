import { prisma } from "./db";

export interface DashboardStats {
 totalCustomers: number;
 totalFilms: number;
 totalRentals: number;
 totalRevenue: number;
 activeRentals: number;
 topCategories: Array<{ name: string; count: number }>;
 revenueByMonth: Array<{ month: string; revenue: number }>;
 topFilms: Array<{ title: string; rentals: number }>;
}

export async function getDashboardStats(): Promise<DashboardStats> {
 try {
  // Get basic counts
  const [totalCustomers, totalFilms, totalRentals, activeRentals] =
   await Promise.all([
    prisma.customer.count(),
    prisma.film.count(),
    prisma.rental.count(),
    prisma.rental.count({
     where: {
      returnDate: null,
     },
    }),
   ]);

  // Get total revenue
  const revenueResult = await prisma.payment.aggregate({
   _sum: {
    amount: true,
   },
  });
  const totalRevenue = Number(revenueResult._sum.amount) || 0;

  // Get top categories using raw SQL for better performance
  const topCategories = await prisma.$queryRaw<
   Array<{ name: string; count: number }>
  >`
      SELECT
        c.name,
        COUNT(r.rental_id)::int as count
      FROM category c
      LEFT JOIN film_category fc ON c.category_id = fc.category_id
      LEFT JOIN film f ON fc.film_id = f.film_id
      LEFT JOIN inventory i ON f.film_id = i.film_id
      LEFT JOIN rental r ON i.inventory_id = r.inventory_id
      GROUP BY c.category_id, c.name
      ORDER BY count DESC
      LIMIT 5
    `;

  // Get revenue by month (last 12 months)
  const revenueByMonth = await prisma.$queryRaw<
   Array<{ month: string; revenue: number }>
  >`
      SELECT
        TO_CHAR(payment_date, 'YYYY-MM') as month,
        SUM(amount)::float as revenue
      FROM payment
      WHERE payment_date >= NOW() - INTERVAL '12 months'
      GROUP BY TO_CHAR(payment_date, 'YYYY-MM')
      ORDER BY month DESC
      LIMIT 12
    `;

  // Get top films by rental count using raw SQL
  const topFilms = await prisma.$queryRaw<
   Array<{ title: string; rentals: number }>
  >`
    SELECT
      f.title,
      COUNT(r.rental_id)::int as rentals
    FROM film f
    LEFT JOIN inventory i ON f.film_id = i.film_id
    LEFT JOIN rental r ON i.inventory_id = r.inventory_id
    GROUP BY f.film_id, f.title
    ORDER BY rentals DESC
    LIMIT 10
  `;

  return {
   totalCustomers,
   totalFilms,
   totalRentals,
   totalRevenue,
   activeRentals,
   topCategories: topCategories,
   revenueByMonth: revenueByMonth.reverse(), // Show oldest to newest
   topFilms: topFilms,
  };
 } catch (error) {
  console.error("Error fetching dashboard stats:", error);
  throw new Error("Failed to fetch dashboard statistics");
 }
}

export async function getCustomerAnalytics() {
 try {
  const customerStats = await prisma.$queryRaw<
   Array<{
    customer_id: number;
    first_name: string;
    last_name: string;
    email: string;
    total_rentals: number;
    total_spent: number;
    last_rental: Date;
   }>
  >`
      SELECT
        c.customer_id,
        c.first_name,
        c.last_name,
        c.email,
        COUNT(r.rental_id)::int as total_rentals,
        COALESCE(SUM(p.amount), 0)::float as total_spent,
        MAX(r.rental_date) as last_rental
      FROM customer c
      LEFT JOIN rental r ON c.customer_id = r.customer_id
      LEFT JOIN payment p ON r.rental_id = p.rental_id
      GROUP BY c.customer_id, c.first_name, c.last_name, c.email
      ORDER BY total_spent DESC
      LIMIT 100
    `;

  return customerStats;
 } catch (error) {
  console.error("Error fetching customer analytics:", error);
  throw new Error("Failed to fetch customer analytics");
 }
}

export async function getFilmAnalytics() {
 try {
  const filmStats = await prisma.$queryRaw<
   Array<{
    film_id: number;
    title: string;
    category: string;
    rental_rate: number;
    total_rentals: number;
    total_revenue: number;
    avg_rental_duration: number;
   }>
  >`
      SELECT
        f.film_id,
        f.title,
        c.name as category,
        f.rental_rate::float,
        COUNT(r.rental_id)::int as total_rentals,
        COALESCE(SUM(p.amount), 0)::float as total_revenue,
        COALESCE(AVG(EXTRACT(DAY FROM (r.return_date - r.rental_date))), 0)::float as avg_rental_duration
      FROM film f
      LEFT JOIN film_category fc ON f.film_id = fc.film_id
      LEFT JOIN category c ON fc.category_id = c.category_id
      LEFT JOIN inventory i ON f.film_id = i.film_id
      LEFT JOIN rental r ON i.inventory_id = r.inventory_id
      LEFT JOIN payment p ON r.rental_id = p.rental_id
      GROUP BY f.film_id, f.title, c.name, f.rental_rate
      ORDER BY total_revenue DESC
      LIMIT 100
    `;

  return filmStats;
 } catch (error) {
  console.error("Error fetching film analytics:", error);
  throw new Error("Failed to fetch film analytics");
 }
}
