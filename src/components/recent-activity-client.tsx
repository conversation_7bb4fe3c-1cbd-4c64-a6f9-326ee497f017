"use client"

import { useEffect, useState } from "react"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Skeleton } from "@/components/ui/skeleton"
import { formatDistanceToNow } from "date-fns"

interface RecentRental {
  rentalId: number
  rentalDate: string
  returnDate: string | null
  customer: {
    firstName: string
    lastName: string
  }
  inventory: {
    film: {
      title: string
    }
  }
  staff: {
    firstName: string
    lastName: string
  }
}

export function RecentActivityClient() {
  const [rentals, setRentals] = useState<RecentRental[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    async function fetchRecentActivity() {
      try {
        const response = await fetch('/api/recent-activity')
        if (!response.ok) {
          throw new Error('Failed to fetch recent activity')
        }
        const data = await response.json()
        setRentals(data)
      } catch (err) {
        setError(err instanceof Error ? err.message : 'An error occurred')
      } finally {
        setLoading(false)
      }
    }

    fetchRecentActivity()
  }, [])

  if (loading) {
    return (
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>Recent Rentals</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="space-y-4">
            {Array.from({ length: 5 }).map((_, i) => (
              <div key={i} className="flex items-center justify-between border-b pb-4">
                <div className="space-y-2">
                  <Skeleton className="h-4 w-48" />
                  <Skeleton className="h-3 w-32" />
                  <Skeleton className="h-3 w-24" />
                </div>
                <div className="space-y-2">
                  <Skeleton className="h-6 w-16" />
                  <Skeleton className="h-3 w-20" />
                </div>
              </div>
            ))}
          </div>
        </CardContent>
      </Card>
    )
  }

  if (error) {
    return (
      <Card className="col-span-2">
        <CardHeader>
          <CardTitle>Recent Rentals</CardTitle>
        </CardHeader>
        <CardContent>
          <p className="text-destructive">Error loading recent activity: {error}</p>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Recent Rentals</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {rentals.length === 0 ? (
            <p className="text-muted-foreground">No recent rentals found.</p>
          ) : (
            rentals.map((rental) => (
              <div key={rental.rentalId} className="flex items-center justify-between border-b pb-4 last:border-b-0">
                <div className="space-y-1">
                  <p className="text-sm font-medium">
                    {rental.inventory.film.title}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Rented by {rental.customer.firstName} {rental.customer.lastName}
                  </p>
                  <p className="text-xs text-muted-foreground">
                    Staff: {rental.staff.firstName} {rental.staff.lastName}
                  </p>
                </div>
                <div className="text-right space-y-1">
                  <Badge variant={rental.returnDate ? "secondary" : "default"}>
                    {rental.returnDate ? "Returned" : "Active"}
                  </Badge>
                  <p className="text-xs text-muted-foreground">
                    {formatDistanceToNow(new Date(rental.rentalDate), { addSuffix: true })}
                  </p>
                </div>
              </div>
            ))
          )}
        </div>
      </CardContent>
    </Card>
  )
}
