import { getDashboardStats } from "@/lib/analytics"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Users, Film, Calendar, DollarSign, Clock } from "lucide-react"

export async function DashboardStats() {
  const stats = await getDashboardStats()

  const statCards = [
    {
      title: "Total Customers",
      value: stats.totalCustomers.toLocaleString(),
      icon: Users,
      description: "Active customers"
    },
    {
      title: "Total Films",
      value: stats.totalFilms.toLocaleString(),
      icon: Film,
      description: "Films in catalog"
    },
    {
      title: "Total Rentals",
      value: stats.totalRentals.toLocaleString(),
      icon: Calendar,
      description: "All-time rentals"
    },
    {
      title: "Total Revenue",
      value: `$${stats.totalRevenue.toLocaleString()}`,
      icon: DollarSign,
      description: "Total earnings"
    },
    {
      title: "Active Rentals",
      value: stats.activeRentals.toLocaleString(),
      icon: Clock,
      description: "Currently rented"
    }
  ]

  return (
    <div className="grid gap-4 md:grid-cols-2 lg:grid-cols-4">
      {statCards.map((stat, index) => (
        <Card key={index}>
          <CardHeader className="flex flex-row items-center justify-between space-y-0 pb-2">
            <CardTitle className="text-sm font-medium">
              {stat.title}
            </CardTitle>
            <stat.icon className="h-4 w-4 text-muted-foreground" />
          </CardHeader>
          <CardContent>
            <div className="text-2xl font-bold">{stat.value}</div>
            <p className="text-xs text-muted-foreground">
              {stat.description}
            </p>
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
