import { prisma } from "@/lib/db"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>eader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { formatDistanceToNow } from "date-fns"

export async function RecentActivity() {
  const recentRentals = await prisma.rental.findMany({
    take: 10,
    orderBy: {
      rentalDate: 'desc'
    },
    include: {
      customer: true,
      inventory: {
        include: {
          film: true
        }
      },
      staff: true
    }
  })

  return (
    <Card className="col-span-2">
      <CardHeader>
        <CardTitle>Recent Rentals</CardTitle>
      </CardHeader>
      <CardContent>
        <div className="space-y-4">
          {recentRentals.map((rental) => (
            <div key={rental.rentalId} className="flex items-center justify-between border-b pb-4 last:border-b-0">
              <div className="space-y-1">
                <p className="text-sm font-medium">
                  {rental.inventory.film.title}
                </p>
                <p className="text-xs text-muted-foreground">
                  Rented by {rental.customer.firstName} {rental.customer.lastName}
                </p>
                <p className="text-xs text-muted-foreground">
                  Staff: {rental.staff.firstName} {rental.staff.lastName}
                </p>
              </div>
              <div className="text-right space-y-1">
                <Badge variant={rental.returnDate ? "secondary" : "default"}>
                  {rental.returnDate ? "Returned" : "Active"}
                </Badge>
                <p className="text-xs text-muted-foreground">
                  {formatDistanceToNow(rental.rentalDate, { addSuffix: true })}
                </p>
              </div>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
