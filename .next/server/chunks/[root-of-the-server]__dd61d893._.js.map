{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/dvdrental%20dashbaord/dvdrental_augment/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/dvdrental%20dashbaord/dvdrental_augment/src/app/api/films/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const search = searchParams.get('search') || ''\n    const category = searchParams.get('category') || ''\n    const rating = searchParams.get('rating') || ''\n    \n    const skip = (page - 1) * limit\n\n    const where: any = {}\n    \n    if (search) {\n      where.OR = [\n        { title: { contains: search, mode: 'insensitive' as const } },\n        { description: { contains: search, mode: 'insensitive' as const } }\n      ]\n    }\n\n    if (category) {\n      where.filmCategories = {\n        some: {\n          category: {\n            name: { equals: category, mode: 'insensitive' as const }\n          }\n        }\n      }\n    }\n\n    if (rating) {\n      where.rating = rating\n    }\n\n    const [films, total] = await Promise.all([\n      prisma.film.findMany({\n        where,\n        skip,\n        take: limit,\n        include: {\n          language: true,\n          filmCategories: {\n            include: {\n              category: true\n            }\n          },\n          _count: {\n            select: {\n              inventory: true\n            }\n          }\n        },\n        orderBy: {\n          title: 'asc'\n        }\n      }),\n      prisma.film.count({ where })\n    ])\n\n    return NextResponse.json({\n      films,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    })\n  } catch (error) {\n    console.error('Films API error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch films' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { \n      title, \n      description, \n      releaseYear, \n      languageId, \n      rentalDuration, \n      rentalRate, \n      length, \n      replacementCost, \n      rating, \n      specialFeatures,\n      categoryIds \n    } = body\n\n    const film = await prisma.film.create({\n      data: {\n        title,\n        description,\n        releaseYear,\n        languageId,\n        rentalDuration: rentalDuration || 3,\n        rentalRate: rentalRate || 4.99,\n        length,\n        replacementCost: replacementCost || 19.99,\n        rating: rating || 'G',\n        specialFeatures: specialFeatures || [],\n        filmCategories: categoryIds ? {\n          create: categoryIds.map((categoryId: number) => ({\n            categoryId\n          }))\n        } : undefined\n      },\n      include: {\n        language: true,\n        filmCategories: {\n          include: {\n            category: true\n          }\n        }\n      }\n    })\n\n    return NextResponse.json(film, { status: 201 })\n  } catch (error) {\n    console.error('Create film error:', error)\n    return NextResponse.json(\n      { error: 'Failed to create film' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAC7C,MAAM,WAAW,aAAa,GAAG,CAAC,eAAe;QACjD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAE7C,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,MAAM,QAAa,CAAC;QAEpB,IAAI,QAAQ;YACV,MAAM,EAAE,GAAG;gBACT;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAuB;gBAAE;gBAC5D;oBAAE,aAAa;wBAAE,UAAU;wBAAQ,MAAM;oBAAuB;gBAAE;aACnE;QACH;QAEA,IAAI,UAAU;YACZ,MAAM,cAAc,GAAG;gBACrB,MAAM;oBACJ,UAAU;wBACR,MAAM;4BAAE,QAAQ;4BAAU,MAAM;wBAAuB;oBACzD;gBACF;YACF;QACF;QAEA,IAAI,QAAQ;YACV,MAAM,MAAM,GAAG;QACjB;QAEA,MAAM,CAAC,OAAO,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YACvC,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,QAAQ,CAAC;gBACnB;gBACA;gBACA,MAAM;gBACN,SAAS;oBACP,UAAU;oBACV,gBAAgB;wBACd,SAAS;4BACP,UAAU;wBACZ;oBACF;oBACA,QAAQ;wBACN,QAAQ;4BACN,WAAW;wBACb;oBACF;gBACF;gBACA,SAAS;oBACP,OAAO;gBACT;YACF;YACA,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC3B;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,oBAAoB;QAClC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EACJ,KAAK,EACL,WAAW,EACX,WAAW,EACX,UAAU,EACV,cAAc,EACd,UAAU,EACV,MAAM,EACN,eAAe,EACf,MAAM,EACN,eAAe,EACf,WAAW,EACZ,GAAG;QAEJ,MAAM,OAAO,MAAM,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,MAAM,CAAC;YACpC,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA,gBAAgB,kBAAkB;gBAClC,YAAY,cAAc;gBAC1B;gBACA,iBAAiB,mBAAmB;gBACpC,QAAQ,UAAU;gBAClB,iBAAiB,mBAAmB,EAAE;gBACtC,gBAAgB,cAAc;oBAC5B,QAAQ,YAAY,GAAG,CAAC,CAAC,aAAuB,CAAC;4BAC/C;wBACF,CAAC;gBACH,IAAI;YACN;YACA,SAAS;gBACP,UAAU;gBACV,gBAAgB;oBACd,SAAS;wBACP,UAAU;oBACZ;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,MAAM;YAAE,QAAQ;QAAI;IAC/C,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,sBAAsB;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAwB,GACjC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}