{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/dvdrental%20dashbaord/dvdrental_augment/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/dvdrental%20dashbaord/dvdrental_augment/src/app/api/customers/route.ts"], "sourcesContent": ["import { NextRequest, NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\n\nexport async function GET(request: NextRequest) {\n  try {\n    const { searchParams } = new URL(request.url)\n    const page = parseInt(searchParams.get('page') || '1')\n    const limit = parseInt(searchParams.get('limit') || '10')\n    const search = searchParams.get('search') || ''\n    \n    const skip = (page - 1) * limit\n\n    const where = search\n      ? {\n          OR: [\n            { firstName: { contains: search, mode: 'insensitive' as const } },\n            { lastName: { contains: search, mode: 'insensitive' as const } },\n            { email: { contains: search, mode: 'insensitive' as const } }\n          ]\n        }\n      : {}\n\n    const [customers, total] = await Promise.all([\n      prisma.customer.findMany({\n        where,\n        skip,\n        take: limit,\n        include: {\n          address: {\n            include: {\n              city: {\n                include: {\n                  country: true\n                }\n              }\n            }\n          },\n          _count: {\n            select: {\n              rentals: true,\n              payments: true\n            }\n          }\n        },\n        orderBy: {\n          customerId: 'asc'\n        }\n      }),\n      prisma.customer.count({ where })\n    ])\n\n    return NextResponse.json({\n      customers,\n      pagination: {\n        page,\n        limit,\n        total,\n        pages: Math.ceil(total / limit)\n      }\n    })\n  } catch (error) {\n    console.error('Customers API error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch customers' },\n      { status: 500 }\n    )\n  }\n}\n\nexport async function POST(request: NextRequest) {\n  try {\n    const body = await request.json()\n    const { firstName, lastName, email, addressId, storeId } = body\n\n    const customer = await prisma.customer.create({\n      data: {\n        firstName,\n        lastName,\n        email,\n        addressId,\n        storeId,\n        activebool: true,\n        createDate: new Date(),\n        active: 1\n      },\n      include: {\n        address: {\n          include: {\n            city: {\n              include: {\n                country: true\n              }\n            }\n          }\n        }\n      }\n    })\n\n    return NextResponse.json(customer, { status: 201 })\n  } catch (error) {\n    console.error('Create customer error:', error)\n    return NextResponse.json(\n      { error: 'Failed to create customer' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;;AAAA;AACA;;;AAEO,eAAe,IAAI,OAAoB;IAC5C,IAAI;QACF,MAAM,EAAE,YAAY,EAAE,GAAG,IAAI,IAAI,QAAQ,GAAG;QAC5C,MAAM,OAAO,SAAS,aAAa,GAAG,CAAC,WAAW;QAClD,MAAM,QAAQ,SAAS,aAAa,GAAG,CAAC,YAAY;QACpD,MAAM,SAAS,aAAa,GAAG,CAAC,aAAa;QAE7C,MAAM,OAAO,CAAC,OAAO,CAAC,IAAI;QAE1B,MAAM,QAAQ,SACV;YACE,IAAI;gBACF;oBAAE,WAAW;wBAAE,UAAU;wBAAQ,MAAM;oBAAuB;gBAAE;gBAChE;oBAAE,UAAU;wBAAE,UAAU;wBAAQ,MAAM;oBAAuB;gBAAE;gBAC/D;oBAAE,OAAO;wBAAE,UAAU;wBAAQ,MAAM;oBAAuB;gBAAE;aAC7D;QACH,IACA,CAAC;QAEL,MAAM,CAAC,WAAW,MAAM,GAAG,MAAM,QAAQ,GAAG,CAAC;YAC3C,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,QAAQ,CAAC;gBACvB;gBACA;gBACA,MAAM;gBACN,SAAS;oBACP,SAAS;wBACP,SAAS;4BACP,MAAM;gCACJ,SAAS;oCACP,SAAS;gCACX;4BACF;wBACF;oBACF;oBACA,QAAQ;wBACN,QAAQ;4BACN,SAAS;4BACT,UAAU;wBACZ;oBACF;gBACF;gBACA,SAAS;oBACP,YAAY;gBACd;YACF;YACA,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK,CAAC;gBAAE;YAAM;SAC/B;QAED,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;YACvB;YACA,YAAY;gBACV;gBACA;gBACA;gBACA,OAAO,KAAK,IAAI,CAAC,QAAQ;YAC3B;QACF;IACF,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF;AAEO,eAAe,KAAK,OAAoB;IAC7C,IAAI;QACF,MAAM,OAAO,MAAM,QAAQ,IAAI;QAC/B,MAAM,EAAE,SAAS,EAAE,QAAQ,EAAE,KAAK,EAAE,SAAS,EAAE,OAAO,EAAE,GAAG;QAE3D,MAAM,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,MAAM,CAAC;YAC5C,MAAM;gBACJ;gBACA;gBACA;gBACA;gBACA;gBACA,YAAY;gBACZ,YAAY,IAAI;gBAChB,QAAQ;YACV;YACA,SAAS;gBACP,SAAS;oBACP,SAAS;wBACP,MAAM;4BACJ,SAAS;gCACP,SAAS;4BACX;wBACF;oBACF;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC,UAAU;YAAE,QAAQ;QAAI;IACnD,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,0BAA0B;QACxC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAA4B,GACrC;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}