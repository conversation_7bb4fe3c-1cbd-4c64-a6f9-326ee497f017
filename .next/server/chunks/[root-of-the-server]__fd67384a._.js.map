{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/dvdrental%20dashbaord/dvdrental_augment/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/dvdrental%20dashbaord/dvdrental_augment/src/lib/analytics.ts"], "sourcesContent": ["import { prisma } from \"./db\";\n\nexport interface DashboardStats {\n totalCustomers: number;\n totalFilms: number;\n totalRentals: number;\n totalRevenue: number;\n activeRentals: number;\n topCategories: Array<{ name: string; count: number }>;\n revenueByMonth: Array<{ month: string; revenue: number }>;\n topFilms: Array<{ title: string; rentals: number }>;\n}\n\nexport async function getDashboardStats(): Promise<DashboardStats> {\n try {\n  // Get basic counts\n  const [totalCustomers, totalFilms, totalRentals, activeRentals] =\n   await Promise.all([\n    prisma.customer.count(),\n    prisma.film.count(),\n    prisma.rental.count(),\n    prisma.rental.count({\n     where: {\n      returnDate: null,\n     },\n    }),\n   ]);\n\n  // Get total revenue\n  const revenueResult = await prisma.payment.aggregate({\n   _sum: {\n    amount: true,\n   },\n  });\n  const totalRevenue = Number(revenueResult._sum.amount) || 0;\n\n  // Get top categories using raw SQL for better performance\n  const topCategories = await prisma.$queryRaw<\n   Array<{ name: string; count: number }>\n  >`\n      SELECT\n        c.name,\n        COUNT(r.rental_id)::int as count\n      FROM category c\n      LEFT JOIN film_category fc ON c.category_id = fc.category_id\n      LEFT JOIN film f ON fc.film_id = f.film_id\n      LEFT JOIN inventory i ON f.film_id = i.film_id\n      LEFT JOIN rental r ON i.inventory_id = r.inventory_id\n      GROUP BY c.category_id, c.name\n      ORDER BY count DESC\n      LIMIT 5\n    `;\n\n  // Get revenue by month (last 12 months)\n  const revenueByMonth = await prisma.$queryRaw<\n   Array<{ month: string; revenue: number }>\n  >`\n      SELECT\n        TO_CHAR(payment_date, 'YYYY-MM') as month,\n        SUM(amount)::float as revenue\n      FROM payment\n      WHERE payment_date >= NOW() - INTERVAL '12 months'\n      GROUP BY TO_CHAR(payment_date, 'YYYY-MM')\n      ORDER BY month DESC\n      LIMIT 12\n    `;\n\n  // Get top films by rental count using raw SQL\n  const topFilms = await prisma.$queryRaw<\n   Array<{ title: string; rentals: number }>\n  >`\n    SELECT\n      f.title,\n      COUNT(r.rental_id)::int as rentals\n    FROM film f\n    LEFT JOIN inventory i ON f.film_id = i.film_id\n    LEFT JOIN rental r ON i.inventory_id = r.inventory_id\n    GROUP BY f.film_id, f.title\n    ORDER BY rentals DESC\n    LIMIT 10\n  `;\n\n  return {\n   totalCustomers,\n   totalFilms,\n   totalRentals,\n   totalRevenue,\n   activeRentals,\n   topCategories: topCategories,\n   revenueByMonth: revenueByMonth.reverse(), // Show oldest to newest\n   topFilms: topFilms,\n  };\n } catch (error) {\n  console.error(\"Error fetching dashboard stats:\", error);\n  throw new Error(\"Failed to fetch dashboard statistics\");\n }\n}\n\nexport async function getCustomerAnalytics() {\n try {\n  const customerStats = await prisma.$queryRaw<\n   Array<{\n    customer_id: number;\n    first_name: string;\n    last_name: string;\n    email: string;\n    total_rentals: number;\n    total_spent: number;\n    last_rental: Date;\n   }>\n  >`\n      SELECT\n        c.customer_id,\n        c.first_name,\n        c.last_name,\n        c.email,\n        COUNT(r.rental_id)::int as total_rentals,\n        COALESCE(SUM(p.amount), 0)::float as total_spent,\n        MAX(r.rental_date) as last_rental\n      FROM customer c\n      LEFT JOIN rental r ON c.customer_id = r.customer_id\n      LEFT JOIN payment p ON r.rental_id = p.rental_id\n      GROUP BY c.customer_id, c.first_name, c.last_name, c.email\n      ORDER BY total_spent DESC\n      LIMIT 100\n    `;\n\n  return customerStats;\n } catch (error) {\n  console.error(\"Error fetching customer analytics:\", error);\n  throw new Error(\"Failed to fetch customer analytics\");\n }\n}\n\nexport async function getFilmAnalytics() {\n try {\n  const filmStats = await prisma.$queryRaw<\n   Array<{\n    film_id: number;\n    title: string;\n    category: string;\n    rental_rate: number;\n    total_rentals: number;\n    total_revenue: number;\n    avg_rental_duration: number;\n   }>\n  >`\n      SELECT\n        f.film_id,\n        f.title,\n        c.name as category,\n        f.rental_rate::float,\n        COUNT(r.rental_id)::int as total_rentals,\n        COALESCE(SUM(p.amount), 0)::float as total_revenue,\n        COALESCE(AVG(EXTRACT(DAY FROM (r.return_date - r.rental_date))), 0)::float as avg_rental_duration\n      FROM film f\n      LEFT JOIN film_category fc ON f.film_id = fc.film_id\n      LEFT JOIN category c ON fc.category_id = c.category_id\n      LEFT JOIN inventory i ON f.film_id = i.film_id\n      LEFT JOIN rental r ON i.inventory_id = r.inventory_id\n      LEFT JOIN payment p ON r.rental_id = p.rental_id\n      GROUP BY f.film_id, f.title, c.name, f.rental_rate\n      ORDER BY total_revenue DESC\n      LIMIT 100\n    `;\n\n  return filmStats;\n } catch (error) {\n  console.error(\"Error fetching film analytics:\", error);\n  throw new Error(\"Failed to fetch film analytics\");\n }\n}\n"], "names": [], "mappings": ";;;;;AAAA;;AAaO,eAAe;IACrB,IAAI;QACH,mBAAmB;QACnB,MAAM,CAAC,gBAAgB,YAAY,cAAc,cAAc,GAC9D,MAAM,QAAQ,GAAG,CAAC;YACjB,kHAAA,CAAA,SAAM,CAAC,QAAQ,CAAC,KAAK;YACrB,kHAAA,CAAA,SAAM,CAAC,IAAI,CAAC,KAAK;YACjB,kHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,KAAK;YACnB,kHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,KAAK,CAAC;gBACnB,OAAO;oBACN,YAAY;gBACb;YACD;SACA;QAEF,oBAAoB;QACpB,MAAM,gBAAgB,MAAM,kHAAA,CAAA,SAAM,CAAC,OAAO,CAAC,SAAS,CAAC;YACpD,MAAM;gBACL,QAAQ;YACT;QACD;QACA,MAAM,eAAe,OAAO,cAAc,IAAI,CAAC,MAAM,KAAK;QAE1D,0DAA0D;QAC1D,MAAM,gBAAgB,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,AAE3C,CAAC;;;;;;;;;;;;IAYA,CAAC;QAEH,wCAAwC;QACxC,MAAM,iBAAiB,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,AAE5C,CAAC;;;;;;;;;IASA,CAAC;QAEH,8CAA8C;QAC9C,MAAM,WAAW,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,AAEtC,CAAC;;;;;;;;;;EAUF,CAAC;QAED,OAAO;YACN;YACA;YACA;YACA;YACA;YACA,eAAe;YACf,gBAAgB,eAAe,OAAO;YACtC,UAAU;QACX;IACD,EAAE,OAAO,OAAO;QACf,QAAQ,KAAK,CAAC,mCAAmC;QACjD,MAAM,IAAI,MAAM;IACjB;AACD;AAEO,eAAe;IACrB,IAAI;QACH,MAAM,gBAAgB,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,AAU3C,CAAC;;;;;;;;;;;;;;;IAeA,CAAC;QAEH,OAAO;IACR,EAAE,OAAO,OAAO;QACf,QAAQ,KAAK,CAAC,sCAAsC;QACpD,MAAM,IAAI,MAAM;IACjB;AACD;AAEO,eAAe;IACrB,IAAI;QACH,MAAM,YAAY,MAAM,kHAAA,CAAA,SAAM,CAAC,SAAS,AAUvC,CAAC;;;;;;;;;;;;;;;;;;IAkBA,CAAC;QAEH,OAAO;IACR,EAAE,OAAO,OAAO;QACf,QAAQ,KAAK,CAAC,kCAAkC;QAChD,MAAM,IAAI,MAAM;IACjB;AACD", "debugId": null}}, {"offset": {"line": 224, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/dvdrental%20dashbaord/dvdrental_augment/src/app/api/dashboard/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport { getDashboardStats } from '@/lib/analytics'\n\nexport async function GET() {\n  try {\n    const stats = await getDashboardStats()\n    return NextResponse.json(stats)\n  } catch (error) {\n    console.error('Dashboard API error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch dashboard data' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,QAAQ,MAAM,CAAA,GAAA,yHAAA,CAAA,oBAAiB,AAAD;QACpC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,wBAAwB;QACtC,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAiC,GAC1C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}