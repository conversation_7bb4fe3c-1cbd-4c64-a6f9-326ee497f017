exports.id=124,exports.ids=[124],exports.modules={270:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,5227,23)),Promise.resolve().then(c.t.bind(c,6346,23)),Promise.resolve().then(c.t.bind(c,7924,23)),Promise.resolve().then(c.t.bind(c,99,23)),Promise.resolve().then(c.t.bind(c,8243,23)),Promise.resolve().then(c.t.bind(c,8827,23)),Promise.resolve().then(c.t.bind(c,2763,23)),Promise.resolve().then(c.t.bind(c,7173,23)),Promise.resolve().then(c.bind(c,5587))},440:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>e});var d=c(1658);let e=async a=>[{type:"image/x-icon",sizes:"16x16",url:(0,d.fillMetadataSegment)(".",await a.params,"favicon.ico")+""}]},1135:()=>{},2265:(a,b,c)=>{"use strict";c.d(b,{DashboardLayout:()=>V});var d=c(687),e=c(3210),f=c(8730),g=c(4224),h=c(4780);let i=(0,g.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function j({className:a,variant:b,size:c,asChild:e=!1,...g}){let j=e?f.DX:"button";return(0,d.jsx)(j,{"data-slot":"button",className:(0,h.cn)(i({variant:b,size:c,className:a})),...g})}var k=c(6134),l=c(1860);function m({...a}){return(0,d.jsx)(k.bL,{"data-slot":"sheet",...a})}function n({...a}){return(0,d.jsx)(k.l9,{"data-slot":"sheet-trigger",...a})}function o({...a}){return(0,d.jsx)(k.ZL,{"data-slot":"sheet-portal",...a})}function p({className:a,...b}){return(0,d.jsx)(k.hJ,{"data-slot":"sheet-overlay",className:(0,h.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",a),...b})}function q({className:a,children:b,side:c="right",...e}){return(0,d.jsxs)(o,{children:[(0,d.jsx)(p,{}),(0,d.jsxs)(k.UC,{"data-slot":"sheet-content",className:(0,h.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===c&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===c&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===c&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===c&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",a),...e,children:[b,(0,d.jsxs)(k.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,d.jsx)(l.A,{className:"size-4"}),(0,d.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var r=c(5814),s=c.n(r),t=c(6189),u=c(2192),v=c(3411),w=c(1312),x=c(8440),y=c(228),z=c(3508),A=c(6001),B=c(22),C=c(4027);let D=[{name:"Dashboard",href:"/",icon:u.A},{name:"Analytics",href:"/analytics",icon:v.A},{name:"Customers",href:"/customers",icon:w.A},{name:"Films",href:"/films",icon:x.A},{name:"Rentals",href:"/rentals",icon:y.A},{name:"Staff",href:"/staff",icon:z.A},{name:"Stores",href:"/stores",icon:A.A},{name:"Reports",href:"/reports",icon:B.A},{name:"Settings",href:"/settings",icon:C.A}];function E(){let a=(0,t.usePathname)();return(0,d.jsx)("nav",{className:"space-y-1",children:D.map(b=>{let c=a===b.href;return(0,d.jsxs)(s(),{href:b.href,className:(0,h.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",c?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-muted"),children:[(0,d.jsx)(b.icon,{className:"mr-3 h-4 w-4"}),b.name]},b.name)})})}var F=c(2941),G=c(9270),H=c(7051),I=c(8869);function J({className:a,type:b,...c}){return(0,d.jsx)("input",{type:b,"data-slot":"input",className:(0,h.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",a),...c})}var K=c(8109);function L({...a}){return(0,d.jsx)(K.bL,{"data-slot":"dropdown-menu",...a})}function M({...a}){return(0,d.jsx)(K.l9,{"data-slot":"dropdown-menu-trigger",...a})}function N({className:a,sideOffset:b=4,...c}){return(0,d.jsx)(K.ZL,{children:(0,d.jsx)(K.UC,{"data-slot":"dropdown-menu-content",sideOffset:b,className:(0,h.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",a),...c})})}function O({className:a,inset:b,variant:c="default",...e}){return(0,d.jsx)(K.q7,{"data-slot":"dropdown-menu-item","data-inset":b,"data-variant":c,className:(0,h.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",a),...e})}function P({className:a,inset:b,...c}){return(0,d.jsx)(K.JU,{"data-slot":"dropdown-menu-label","data-inset":b,className:(0,h.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",a),...c})}function Q({className:a,...b}){return(0,d.jsx)(K.wv,{"data-slot":"dropdown-menu-separator",className:(0,h.cn)("bg-border -mx-1 my-1 h-px",a),...b})}var R=c(1096);function S({className:a,...b}){return(0,d.jsx)(R.bL,{"data-slot":"avatar",className:(0,h.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",a),...b})}function T({className:a,...b}){return(0,d.jsx)(R._V,{"data-slot":"avatar-image",className:(0,h.cn)("aspect-square size-full",a),...b})}function U({className:a,...b}){return(0,d.jsx)(R.H4,{"data-slot":"avatar-fallback",className:(0,h.cn)("bg-muted flex size-full items-center justify-center rounded-full",a),...b})}function V({children:a}){let[b,c]=(0,e.useState)(!1);return(0,d.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,d.jsxs)(m,{open:b,onOpenChange:c,children:[(0,d.jsx)(n,{asChild:!0,children:(0,d.jsx)(j,{variant:"ghost",size:"icon",className:"fixed top-4 left-4 z-40 md:hidden",children:(0,d.jsx)(F.A,{className:"h-6 w-6"})})}),(0,d.jsx)(q,{side:"left",className:"w-64 p-0",children:(0,d.jsxs)("div",{className:"flex h-full flex-col",children:[(0,d.jsx)("div",{className:"flex h-16 items-center border-b px-6",children:(0,d.jsx)("h1",{className:"text-lg font-semibold",children:"DVD Rental"})}),(0,d.jsx)("div",{className:"flex-1 overflow-auto p-6",children:(0,d.jsx)(E,{})})]})})]}),(0,d.jsx)("div",{className:"hidden md:fixed md:inset-y-0 md:flex md:w-64 md:flex-col",children:(0,d.jsxs)("div",{className:"flex min-h-0 flex-1 flex-col border-r bg-card",children:[(0,d.jsx)("div",{className:"flex h-16 items-center border-b px-6",children:(0,d.jsx)("h1",{className:"text-lg font-semibold",children:"DVD Rental Dashboard"})}),(0,d.jsx)("div",{className:"flex flex-1 flex-col overflow-y-auto p-6",children:(0,d.jsx)(E,{})})]})}),(0,d.jsxs)("div",{className:"md:pl-64",children:[(0,d.jsxs)("header",{className:"sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6",children:[(0,d.jsx)("div",{className:"flex flex-1 items-center gap-4",children:(0,d.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,d.jsx)(G.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,d.jsx)(J,{placeholder:"Search customers, films, rentals...",className:"pl-10"})]})}),(0,d.jsxs)("div",{className:"flex items-center gap-4",children:[(0,d.jsx)(j,{variant:"ghost",size:"icon",children:(0,d.jsx)(H.A,{className:"h-5 w-5"})}),(0,d.jsxs)(L,{children:[(0,d.jsx)(M,{asChild:!0,children:(0,d.jsx)(j,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,d.jsxs)(S,{className:"h-8 w-8",children:[(0,d.jsx)(T,{src:"/avatars/01.png",alt:"@admin"}),(0,d.jsx)(U,{children:"AD"})]})})}),(0,d.jsxs)(N,{className:"w-56",align:"end",forceMount:!0,children:[(0,d.jsx)(P,{className:"font-normal",children:(0,d.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,d.jsx)("p",{className:"text-sm font-medium leading-none",children:"Admin User"}),(0,d.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:"<EMAIL>"})]})}),(0,d.jsx)(Q,{}),(0,d.jsxs)(O,{children:[(0,d.jsx)(I.A,{className:"mr-2 h-4 w-4"}),(0,d.jsx)("span",{children:"Profile"})]}),(0,d.jsx)(O,{children:(0,d.jsx)("span",{children:"Settings"})}),(0,d.jsx)(Q,{}),(0,d.jsx)(O,{children:(0,d.jsx)("span",{children:"Log out"})})]})]})]})]}),(0,d.jsx)("main",{className:"flex-1 p-4 md:p-6",children:a})]})]})}},3233:(a,b,c)=>{"use strict";c.d(b,{DashboardLayout:()=>d});let d=(0,c(1369).registerClientReference)(function(){throw Error("Attempted to call DashboardLayout() from the server but DashboardLayout is on the client. It's not possible to invoke a client function from the server, it can only be rendered as a Component or passed to props of a Client Component.")},"/home/<USER>/Projects/dvdrental dashbaord/dvdrental_augment/src/components/dashboard-layout.tsx","DashboardLayout")},4431:(a,b,c)=>{"use strict";c.r(b),c.d(b,{default:()=>k,metadata:()=>j});var d=c(7413),e=c(2202),f=c.n(e),g=c(4988),h=c.n(g);c(1135);var i=c(3233);let j={title:"DVD Rental Dashboard",description:"Comprehensive dashboard for DVD rental management"};function k({children:a}){return(0,d.jsx)("html",{lang:"en",children:(0,d.jsx)("body",{className:`${f().variable} ${h().variable} antialiased`,children:(0,d.jsx)(i.DashboardLayout,{children:a})})})}},4780:(a,b,c)=>{"use strict";c.d(b,{cn:()=>f});var d=c(9384),e=c(2348);function f(...a){return(0,e.QP)((0,d.$)(a))}},6710:(a,b,c)=>{Promise.resolve().then(c.t.bind(c,6133,23)),Promise.resolve().then(c.t.bind(c,6444,23)),Promise.resolve().then(c.t.bind(c,6042,23)),Promise.resolve().then(c.t.bind(c,9477,23)),Promise.resolve().then(c.t.bind(c,9345,23)),Promise.resolve().then(c.t.bind(c,2089,23)),Promise.resolve().then(c.t.bind(c,6577,23)),Promise.resolve().then(c.t.bind(c,1307,23)),Promise.resolve().then(c.t.bind(c,4817,23))},8321:(a,b,c)=>{Promise.resolve().then(c.bind(c,2265))},8993:(a,b,c)=>{Promise.resolve().then(c.bind(c,3233))}};