{"version": 3, "sources": [], "sections": [{"offset": {"line": 6, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": "", "debugId": null}}, {"offset": {"line": 74, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/dvdrental%20dashbaord/dvdrental_augment/src/lib/db.ts"], "sourcesContent": ["import { PrismaClient } from '@prisma/client'\n\nconst globalForPrisma = globalThis as unknown as {\n  prisma: PrismaClient | undefined\n}\n\nexport const prisma =\n  globalForPrisma.prisma ??\n  new PrismaClient({\n    log: ['query'],\n  })\n\nif (process.env.NODE_ENV !== 'production') globalForPrisma.prisma = prisma\n"], "names": [], "mappings": ";;;AAAA;;AAEA,MAAM,kBAAkB;AAIjB,MAAM,SACX,gBAAgB,MAAM,IACtB,IAAI,6HAAA,CAAA,eAAY,CAAC;IACf,KAAK;QAAC;KAAQ;AAChB;AAEF,wCAA2C,gBAAgB,MAAM,GAAG", "debugId": null}}, {"offset": {"line": 90, "column": 0}, "map": {"version": 3, "sources": ["file:///home/<USER>/Projects/dvdrental%20dashbaord/dvdrental_augment/src/app/api/recent-activity/route.ts"], "sourcesContent": ["import { NextResponse } from 'next/server'\nimport { prisma } from '@/lib/db'\n\nexport async function GET() {\n  try {\n    const recentRentals = await prisma.rental.findMany({\n      take: 10,\n      orderBy: {\n        rentalDate: 'desc'\n      },\n      include: {\n        customer: {\n          select: {\n            firstName: true,\n            lastName: true\n          }\n        },\n        inventory: {\n          include: {\n            film: {\n              select: {\n                title: true\n              }\n            }\n          }\n        },\n        staff: {\n          select: {\n            firstName: true,\n            lastName: true\n          }\n        }\n      }\n    })\n\n    return NextResponse.json(recentRentals)\n  } catch (error) {\n    console.error('Recent activity API error:', error)\n    return NextResponse.json(\n      { error: 'Failed to fetch recent activity' },\n      { status: 500 }\n    )\n  }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEO,eAAe;IACpB,IAAI;QACF,MAAM,gBAAgB,MAAM,kHAAA,CAAA,SAAM,CAAC,MAAM,CAAC,QAAQ,CAAC;YACjD,MAAM;YACN,SAAS;gBACP,YAAY;YACd;YACA,SAAS;gBACP,UAAU;oBACR,QAAQ;wBACN,WAAW;wBACX,UAAU;oBACZ;gBACF;gBACA,WAAW;oBACT,SAAS;wBACP,MAAM;4BACJ,QAAQ;gCACN,OAAO;4BACT;wBACF;oBACF;gBACF;gBACA,OAAO;oBACL,QAAQ;wBACN,WAAW;wBACX,UAAU;oBACZ;gBACF;YACF;QACF;QAEA,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CAAC;IAC3B,EAAE,OAAO,OAAO;QACd,QAAQ,KAAK,CAAC,8BAA8B;QAC5C,OAAO,gIAAA,CAAA,eAAY,CAAC,IAAI,CACtB;YAAE,OAAO;QAAkC,GAC3C;YAAE,QAAQ;QAAI;IAElB;AACF", "debugId": null}}]}