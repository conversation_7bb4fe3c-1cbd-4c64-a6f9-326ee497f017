(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[974],{605:(e,t,s)=>{Promise.resolve().then(s.bind(s,827))},827:(e,t,s)=>{"use strict";s.r(t),s.d(t,{default:()=>y});var a=s(5155),r=s(2115),n=s(9434);function i(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card",className:(0,n.cn)("bg-card text-card-foreground flex flex-col gap-6 rounded-xl border py-6 shadow-sm",t),...s})}function l(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-header",className:(0,n.cn)("@container/card-header grid auto-rows-min grid-rows-[auto_auto] items-start gap-1.5 px-6 has-data-[slot=card-action]:grid-cols-[1fr_auto] [.border-b]:pb-6",t),...s})}function c(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-title",className:(0,n.cn)("leading-none font-semibold",t),...s})}function d(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"card-content",className:(0,n.cn)("px-6",t),...s})}function o(e){let{className:t,...s}=e;return(0,a.jsx)("div",{"data-slot":"skeleton",className:(0,n.cn)("bg-accent animate-pulse rounded-md",t),...s})}var u=s(7580),x=s(1066),m=s(9074),f=s(5868),h=s(4186);function g(){let[e,t]=(0,r.useState)(null),[s,n]=(0,r.useState)(!0),[g,v]=(0,r.useState)(null);if((0,r.useEffect)(()=>{!async function(){try{let e=await fetch("/api/dashboard");if(!e.ok)throw Error("Failed to fetch dashboard stats");let s=await e.json();t(s)}catch(e){v(e instanceof Error?e.message:"An error occurred")}finally{n(!1)}}()},[]),s)return(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:Array.from({length:5}).map((e,t)=>(0,a.jsx)(o,{className:"h-32"},t))});if(g)return(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:(0,a.jsx)(i,{className:"col-span-full",children:(0,a.jsx)(d,{className:"pt-6",children:(0,a.jsxs)("p",{className:"text-destructive",children:["Error loading dashboard stats: ",g]})})})});if(!e)return null;let j=[{title:"Total Customers",value:e.totalCustomers.toLocaleString(),icon:u.A,description:"Active customers"},{title:"Total Films",value:e.totalFilms.toLocaleString(),icon:x.A,description:"Films in catalog"},{title:"Total Rentals",value:e.totalRentals.toLocaleString(),icon:m.A,description:"All-time rentals"},{title:"Total Revenue",value:"$".concat(e.totalRevenue.toLocaleString()),icon:f.A,description:"Total earnings"},{title:"Active Rentals",value:e.activeRentals.toLocaleString(),icon:h.A,description:"Currently rented"}];return(0,a.jsx)("div",{className:"grid gap-4 md:grid-cols-2 lg:grid-cols-4",children:j.map((e,t)=>(0,a.jsxs)(i,{children:[(0,a.jsxs)(l,{className:"flex flex-row items-center justify-between space-y-0 pb-2",children:[(0,a.jsx)(c,{className:"text-sm font-medium",children:e.title}),(0,a.jsx)(e.icon,{className:"h-4 w-4 text-muted-foreground"})]}),(0,a.jsxs)(d,{children:[(0,a.jsx)("div",{className:"text-2xl font-bold",children:e.value}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:e.description})]})]},t))})}var v=s(9708);let j=(0,s(2085).F)("inline-flex items-center justify-center rounded-md border px-2 py-0.5 text-xs font-medium w-fit whitespace-nowrap shrink-0 [&>svg]:size-3 gap-1 [&>svg]:pointer-events-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive transition-[color,box-shadow] overflow-hidden",{variants:{variant:{default:"border-transparent bg-primary text-primary-foreground [a&]:hover:bg-primary/90",secondary:"border-transparent bg-secondary text-secondary-foreground [a&]:hover:bg-secondary/90",destructive:"border-transparent bg-destructive text-white [a&]:hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"text-foreground [a&]:hover:bg-accent [a&]:hover:text-accent-foreground"}},defaultVariants:{variant:"default"}});function p(e){let{className:t,variant:s,asChild:r=!1,...i}=e,l=r?v.DX:"span";return(0,a.jsx)(l,{"data-slot":"badge",className:(0,n.cn)(j({variant:s}),t),...i})}var N=s(757);function b(){let[e,t]=(0,r.useState)([]),[s,n]=(0,r.useState)(!0),[u,x]=(0,r.useState)(null);return((0,r.useEffect)(()=>{!async function(){try{let e=await fetch("/api/recent-activity");if(!e.ok)throw Error("Failed to fetch recent activity");let s=await e.json();t(s)}catch(e){x(e instanceof Error?e.message:"An error occurred")}finally{n(!1)}}()},[]),s)?(0,a.jsxs)(i,{className:"col-span-2",children:[(0,a.jsx)(l,{children:(0,a.jsx)(c,{children:"Recent Rentals"})}),(0,a.jsx)(d,{children:(0,a.jsx)("div",{className:"space-y-4",children:Array.from({length:5}).map((e,t)=>(0,a.jsxs)("div",{className:"flex items-center justify-between border-b pb-4",children:[(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o,{className:"h-4 w-48"}),(0,a.jsx)(o,{className:"h-3 w-32"}),(0,a.jsx)(o,{className:"h-3 w-24"})]}),(0,a.jsxs)("div",{className:"space-y-2",children:[(0,a.jsx)(o,{className:"h-6 w-16"}),(0,a.jsx)(o,{className:"h-3 w-20"})]})]},t))})})]}):u?(0,a.jsxs)(i,{className:"col-span-2",children:[(0,a.jsx)(l,{children:(0,a.jsx)(c,{children:"Recent Rentals"})}),(0,a.jsx)(d,{children:(0,a.jsxs)("p",{className:"text-destructive",children:["Error loading recent activity: ",u]})})]}):(0,a.jsxs)(i,{className:"col-span-2",children:[(0,a.jsx)(l,{children:(0,a.jsx)(c,{children:"Recent Rentals"})}),(0,a.jsx)(d,{children:(0,a.jsx)("div",{className:"space-y-4",children:0===e.length?(0,a.jsx)("p",{className:"text-muted-foreground",children:"No recent rentals found."}):e.map(e=>(0,a.jsxs)("div",{className:"flex items-center justify-between border-b pb-4 last:border-b-0",children:[(0,a.jsxs)("div",{className:"space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium",children:e.inventory.film.title}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Rented by ",e.customer.firstName," ",e.customer.lastName]}),(0,a.jsxs)("p",{className:"text-xs text-muted-foreground",children:["Staff: ",e.staff.firstName," ",e.staff.lastName]})]}),(0,a.jsxs)("div",{className:"text-right space-y-1",children:[(0,a.jsx)(p,{variant:e.returnDate?"secondary":"default",children:e.returnDate?"Returned":"Active"}),(0,a.jsx)("p",{className:"text-xs text-muted-foreground",children:(0,N.m)(new Date(e.rentalDate),{addSuffix:!0})})]})]},e.rentalId))})})]})}function y(){return(0,a.jsxs)("div",{className:"space-y-6",children:[(0,a.jsxs)("div",{children:[(0,a.jsx)("h1",{className:"text-3xl font-bold tracking-tight",children:"Dashboard"}),(0,a.jsx)("p",{className:"text-muted-foreground",children:"Welcome to your DVD rental management dashboard"})]}),(0,a.jsx)(g,{}),(0,a.jsx)("div",{className:"grid gap-6 md:grid-cols-2",children:(0,a.jsx)(b,{})})]})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>n});var a=s(2596),r=s(9688);function n(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,r.QP)((0,a.$)(t))}}},e=>{e.O(0,[905,313,441,964,358],()=>e(e.s=605)),_N_E=e.O()}]);