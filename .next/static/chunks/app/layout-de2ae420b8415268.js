(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[177],{347:()=>{},3167:(e,t,s)=>{Promise.resolve().then(s.t.bind(s,4147,23)),Promise.resolve().then(s.t.bind(s,8489,23)),Promise.resolve().then(s.t.bind(s,347,23)),Promise.resolve().then(s.bind(s,6300))},6300:(e,t,s)=>{"use strict";s.d(t,{DashboardLayout:()=>B});var a=s(5155),n=s(2115),r=s(9708),i=s(2085),o=s(9434);let d=(0,i.F)("inline-flex items-center justify-center gap-2 whitespace-nowrap rounded-md text-sm font-medium transition-all disabled:pointer-events-none disabled:opacity-50 [&_svg]:pointer-events-none [&_svg:not([class*='size-'])]:size-4 shrink-0 [&_svg]:shrink-0 outline-none focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px] aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",{variants:{variant:{default:"bg-primary text-primary-foreground shadow-xs hover:bg-primary/90",destructive:"bg-destructive text-white shadow-xs hover:bg-destructive/90 focus-visible:ring-destructive/20 dark:focus-visible:ring-destructive/40 dark:bg-destructive/60",outline:"border bg-background shadow-xs hover:bg-accent hover:text-accent-foreground dark:bg-input/30 dark:border-input dark:hover:bg-input/50",secondary:"bg-secondary text-secondary-foreground shadow-xs hover:bg-secondary/80",ghost:"hover:bg-accent hover:text-accent-foreground dark:hover:bg-accent/50",link:"text-primary underline-offset-4 hover:underline"},size:{default:"h-9 px-4 py-2 has-[>svg]:px-3",sm:"h-8 rounded-md gap-1.5 px-3 has-[>svg]:px-2.5",lg:"h-10 rounded-md px-6 has-[>svg]:px-4",icon:"size-9"}},defaultVariants:{variant:"default",size:"default"}});function l(e){let{className:t,variant:s,size:n,asChild:i=!1,...l}=e,c=i?r.DX:"button";return(0,a.jsx)(c,{"data-slot":"button",className:(0,o.cn)(d({variant:s,size:n,className:t})),...l})}var c=s(5452),u=s(4416);function m(e){let{...t}=e;return(0,a.jsx)(c.bL,{"data-slot":"sheet",...t})}function f(e){let{...t}=e;return(0,a.jsx)(c.l9,{"data-slot":"sheet-trigger",...t})}function x(e){let{...t}=e;return(0,a.jsx)(c.ZL,{"data-slot":"sheet-portal",...t})}function h(e){let{className:t,...s}=e;return(0,a.jsx)(c.hJ,{"data-slot":"sheet-overlay",className:(0,o.cn)("data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 fixed inset-0 z-50 bg-black/50",t),...s})}function p(e){let{className:t,children:s,side:n="right",...r}=e;return(0,a.jsxs)(x,{children:[(0,a.jsx)(h,{}),(0,a.jsxs)(c.UC,{"data-slot":"sheet-content",className:(0,o.cn)("bg-background data-[state=open]:animate-in data-[state=closed]:animate-out fixed z-50 flex flex-col gap-4 shadow-lg transition ease-in-out data-[state=closed]:duration-300 data-[state=open]:duration-500","right"===n&&"data-[state=closed]:slide-out-to-right data-[state=open]:slide-in-from-right inset-y-0 right-0 h-full w-3/4 border-l sm:max-w-sm","left"===n&&"data-[state=closed]:slide-out-to-left data-[state=open]:slide-in-from-left inset-y-0 left-0 h-full w-3/4 border-r sm:max-w-sm","top"===n&&"data-[state=closed]:slide-out-to-top data-[state=open]:slide-in-from-top inset-x-0 top-0 h-auto border-b","bottom"===n&&"data-[state=closed]:slide-out-to-bottom data-[state=open]:slide-in-from-bottom inset-x-0 bottom-0 h-auto border-t",t),...r,children:[s,(0,a.jsxs)(c.bm,{className:"ring-offset-background focus:ring-ring data-[state=open]:bg-secondary absolute top-4 right-4 rounded-xs opacity-70 transition-opacity hover:opacity-100 focus:ring-2 focus:ring-offset-2 focus:outline-hidden disabled:pointer-events-none",children:[(0,a.jsx)(u.A,{className:"size-4"}),(0,a.jsx)("span",{className:"sr-only",children:"Close"})]})]})]})}var v=s(6874),g=s.n(v),b=s(5695),j=s(7340),N=s(2713),w=s(7580),y=s(1066),k=s(9074),z=s(5670),A=s(5937),_=s(7434),C=s(381);let D=[{name:"Dashboard",href:"/",icon:j.A},{name:"Analytics",href:"/analytics",icon:N.A},{name:"Customers",href:"/customers",icon:w.A},{name:"Films",href:"/films",icon:y.A},{name:"Rentals",href:"/rentals",icon:k.A},{name:"Staff",href:"/staff",icon:z.A},{name:"Stores",href:"/stores",icon:A.A},{name:"Reports",href:"/reports",icon:_.A},{name:"Settings",href:"/settings",icon:C.A}];function L(){let e=(0,b.usePathname)();return(0,a.jsx)("nav",{className:"space-y-1",children:D.map(t=>{let s=e===t.href;return(0,a.jsxs)(g(),{href:t.href,className:(0,o.cn)("flex items-center px-3 py-2 text-sm font-medium rounded-md transition-colors",s?"bg-primary text-primary-foreground":"text-muted-foreground hover:text-foreground hover:bg-muted"),children:[(0,a.jsx)(t.icon,{className:"mr-3 h-4 w-4"}),t.name]},t.name)})})}var P=s(4783),S=s(7924),O=s(3861),R=s(1007);function U(e){let{className:t,type:s,...n}=e;return(0,a.jsx)("input",{type:s,"data-slot":"input",className:(0,o.cn)("file:text-foreground placeholder:text-muted-foreground selection:bg-primary selection:text-primary-foreground dark:bg-input/30 border-input flex h-9 w-full min-w-0 rounded-md border bg-transparent px-3 py-1 text-base shadow-xs transition-[color,box-shadow] outline-none file:inline-flex file:h-7 file:border-0 file:bg-transparent file:text-sm file:font-medium disabled:pointer-events-none disabled:cursor-not-allowed disabled:opacity-50 md:text-sm","focus-visible:border-ring focus-visible:ring-ring/50 focus-visible:ring-[3px]","aria-invalid:ring-destructive/20 dark:aria-invalid:ring-destructive/40 aria-invalid:border-destructive",t),...n})}var V=s(1853);function E(e){let{...t}=e;return(0,a.jsx)(V.bL,{"data-slot":"dropdown-menu",...t})}function q(e){let{...t}=e;return(0,a.jsx)(V.l9,{"data-slot":"dropdown-menu-trigger",...t})}function F(e){let{className:t,sideOffset:s=4,...n}=e;return(0,a.jsx)(V.ZL,{children:(0,a.jsx)(V.UC,{"data-slot":"dropdown-menu-content",sideOffset:s,className:(0,o.cn)("bg-popover text-popover-foreground data-[state=open]:animate-in data-[state=closed]:animate-out data-[state=closed]:fade-out-0 data-[state=open]:fade-in-0 data-[state=closed]:zoom-out-95 data-[state=open]:zoom-in-95 data-[side=bottom]:slide-in-from-top-2 data-[side=left]:slide-in-from-right-2 data-[side=right]:slide-in-from-left-2 data-[side=top]:slide-in-from-bottom-2 z-50 max-h-(--radix-dropdown-menu-content-available-height) min-w-[8rem] origin-(--radix-dropdown-menu-content-transform-origin) overflow-x-hidden overflow-y-auto rounded-md border p-1 shadow-md",t),...n})})}function J(e){let{className:t,inset:s,variant:n="default",...r}=e;return(0,a.jsx)(V.q7,{"data-slot":"dropdown-menu-item","data-inset":s,"data-variant":n,className:(0,o.cn)("focus:bg-accent focus:text-accent-foreground data-[variant=destructive]:text-destructive data-[variant=destructive]:focus:bg-destructive/10 dark:data-[variant=destructive]:focus:bg-destructive/20 data-[variant=destructive]:focus:text-destructive data-[variant=destructive]:*:[svg]:!text-destructive [&_svg:not([class*='text-'])]:text-muted-foreground relative flex cursor-default items-center gap-2 rounded-sm px-2 py-1.5 text-sm outline-hidden select-none data-[disabled]:pointer-events-none data-[disabled]:opacity-50 data-[inset]:pl-8 [&_svg]:pointer-events-none [&_svg]:shrink-0 [&_svg:not([class*='size-'])]:size-4",t),...r})}function Z(e){let{className:t,inset:s,...n}=e;return(0,a.jsx)(V.JU,{"data-slot":"dropdown-menu-label","data-inset":s,className:(0,o.cn)("px-2 py-1.5 text-sm font-medium data-[inset]:pl-8",t),...n})}function H(e){let{className:t,...s}=e;return(0,a.jsx)(V.wv,{"data-slot":"dropdown-menu-separator",className:(0,o.cn)("bg-border -mx-1 my-1 h-px",t),...s})}var M=s(4011);function Q(e){let{className:t,...s}=e;return(0,a.jsx)(M.bL,{"data-slot":"avatar",className:(0,o.cn)("relative flex size-8 shrink-0 overflow-hidden rounded-full",t),...s})}function X(e){let{className:t,...s}=e;return(0,a.jsx)(M._V,{"data-slot":"avatar-image",className:(0,o.cn)("aspect-square size-full",t),...s})}function $(e){let{className:t,...s}=e;return(0,a.jsx)(M.H4,{"data-slot":"avatar-fallback",className:(0,o.cn)("bg-muted flex size-full items-center justify-center rounded-full",t),...s})}function B(e){let{children:t}=e,[s,r]=(0,n.useState)(!1);return(0,a.jsxs)("div",{className:"min-h-screen bg-background",children:[(0,a.jsxs)(m,{open:s,onOpenChange:r,children:[(0,a.jsx)(f,{asChild:!0,children:(0,a.jsx)(l,{variant:"ghost",size:"icon",className:"fixed top-4 left-4 z-40 md:hidden",children:(0,a.jsx)(P.A,{className:"h-6 w-6"})})}),(0,a.jsx)(p,{side:"left",className:"w-64 p-0",children:(0,a.jsxs)("div",{className:"flex h-full flex-col",children:[(0,a.jsx)("div",{className:"flex h-16 items-center border-b px-6",children:(0,a.jsx)("h1",{className:"text-lg font-semibold",children:"DVD Rental"})}),(0,a.jsx)("div",{className:"flex-1 overflow-auto p-6",children:(0,a.jsx)(L,{})})]})})]}),(0,a.jsx)("div",{className:"hidden md:fixed md:inset-y-0 md:flex md:w-64 md:flex-col",children:(0,a.jsxs)("div",{className:"flex min-h-0 flex-1 flex-col border-r bg-card",children:[(0,a.jsx)("div",{className:"flex h-16 items-center border-b px-6",children:(0,a.jsx)("h1",{className:"text-lg font-semibold",children:"DVD Rental Dashboard"})}),(0,a.jsx)("div",{className:"flex flex-1 flex-col overflow-y-auto p-6",children:(0,a.jsx)(L,{})})]})}),(0,a.jsxs)("div",{className:"md:pl-64",children:[(0,a.jsxs)("header",{className:"sticky top-0 z-30 flex h-16 items-center gap-4 border-b bg-background px-4 md:px-6",children:[(0,a.jsx)("div",{className:"flex flex-1 items-center gap-4",children:(0,a.jsxs)("div",{className:"relative flex-1 max-w-md",children:[(0,a.jsx)(S.A,{className:"absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground"}),(0,a.jsx)(U,{placeholder:"Search customers, films, rentals...",className:"pl-10"})]})}),(0,a.jsxs)("div",{className:"flex items-center gap-4",children:[(0,a.jsx)(l,{variant:"ghost",size:"icon",children:(0,a.jsx)(O.A,{className:"h-5 w-5"})}),(0,a.jsxs)(E,{children:[(0,a.jsx)(q,{asChild:!0,children:(0,a.jsx)(l,{variant:"ghost",className:"relative h-8 w-8 rounded-full",children:(0,a.jsxs)(Q,{className:"h-8 w-8",children:[(0,a.jsx)(X,{src:"/avatars/01.png",alt:"@admin"}),(0,a.jsx)($,{children:"AD"})]})})}),(0,a.jsxs)(F,{className:"w-56",align:"end",forceMount:!0,children:[(0,a.jsx)(Z,{className:"font-normal",children:(0,a.jsxs)("div",{className:"flex flex-col space-y-1",children:[(0,a.jsx)("p",{className:"text-sm font-medium leading-none",children:"Admin User"}),(0,a.jsx)("p",{className:"text-xs leading-none text-muted-foreground",children:"<EMAIL>"})]})}),(0,a.jsx)(H,{}),(0,a.jsxs)(J,{children:[(0,a.jsx)(R.A,{className:"mr-2 h-4 w-4"}),(0,a.jsx)("span",{children:"Profile"})]}),(0,a.jsx)(J,{children:(0,a.jsx)("span",{children:"Settings"})}),(0,a.jsx)(H,{}),(0,a.jsx)(J,{children:(0,a.jsx)("span",{children:"Log out"})})]})]})]})]}),(0,a.jsx)("main",{className:"flex-1 p-4 md:p-6",children:t})]})]})}},9434:(e,t,s)=>{"use strict";s.d(t,{cn:()=>r});var a=s(2596),n=s(9688);function r(){for(var e=arguments.length,t=Array(e),s=0;s<e;s++)t[s]=arguments[s];return(0,n.QP)((0,a.$)(t))}}},e=>{e.O(0,[896,905,0,441,964,358],()=>e(e.s=3167)),_N_E=e.O()}]);