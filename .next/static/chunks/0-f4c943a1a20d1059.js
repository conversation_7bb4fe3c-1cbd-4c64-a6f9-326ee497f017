(self.webpackChunk_N_E=self.webpackChunk_N_E||[]).push([[0],{381:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("settings",[["path",{d:"M9.671 4.136a2.34 2.34 0 0 1 4.659 0 2.34 2.34 0 0 0 3.319 1.915 2.34 2.34 0 0 1 2.33 4.033 2.34 2.34 0 0 0 0 3.831 2.34 2.34 0 0 1-2.33 4.033 2.34 2.34 0 0 0-3.319 1.915 2.34 2.34 0 0 1-4.659 0 2.34 2.34 0 0 0-3.32-1.915 2.34 2.34 0 0 1-2.33-4.033 2.34 2.34 0 0 0 0-3.831A2.34 2.34 0 0 1 6.35 6.051a2.34 2.34 0 0 0 3.319-1.915",key:"1i5ecw"}],["circle",{cx:"12",cy:"12",r:"3",key:"1v7zrd"}]])},1007:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("user",[["path",{d:"M19 21v-2a4 4 0 0 0-4-4H9a4 4 0 0 0-4 4v2",key:"975kel"}],["circle",{cx:"12",cy:"7",r:"4",key:"17ys0d"}]])},1285:(e,t,n)=>{"use strict";n.d(t,{B:()=>u});var r,o=n(2115),i=n(2712),a=(r||(r=n.t(o,2)))[" useId ".trim().toString()]||(()=>void 0),l=0;function u(e){let[t,n]=o.useState(a());return(0,i.N)(()=>{e||n(e=>e??String(l++))},[e]),e||(t?`radix-${t}`:"")}},1414:(e,t,n)=>{"use strict";e.exports=n(2436)},1853:(e,t,n)=>{"use strict";n.d(t,{UC:()=>nB,q7:()=>nz,JU:()=>nU,ZL:()=>nW,bL:()=>nI,wv:()=>nK,l9:()=>nF});var r,o=n(2115),i=n(5185),a=n(6101),l=n(6081),u=n(5845),s=n(3655);function c(e,t,n){if(!t.has(e))throw TypeError("attempted to "+n+" private field on non-instance");return t.get(e)}function f(e,t){var n=c(e,t,"get");return n.get?n.get.call(e):n.value}function d(e,t,n){var r=c(e,t,"set");if(r.set)r.set.call(e,n);else{if(!r.writable)throw TypeError("attempted to set read only private field");r.value=n}return n}var p=n(9708),h=n(5155);function m(e){let t=e+"CollectionProvider",[n,r]=(0,l.A)(t),[i,u]=n(t,{collectionRef:{current:null},itemMap:new Map}),s=e=>{let{scope:t,children:n}=e,r=o.useRef(null),a=o.useRef(new Map).current;return(0,h.jsx)(i,{scope:t,itemMap:a,collectionRef:r,children:n})};s.displayName=t;let c=e+"CollectionSlot",f=(0,p.TL)(c),d=o.forwardRef((e,t)=>{let{scope:n,children:r}=e,o=u(c,n),i=(0,a.s)(t,o.collectionRef);return(0,h.jsx)(f,{ref:i,children:r})});d.displayName=c;let m=e+"CollectionItemSlot",v="data-radix-collection-item",g=(0,p.TL)(m),y=o.forwardRef((e,t)=>{let{scope:n,children:r,...i}=e,l=o.useRef(null),s=(0,a.s)(t,l),c=u(m,n);return o.useEffect(()=>(c.itemMap.set(l,{ref:l,...i}),()=>void c.itemMap.delete(l))),(0,h.jsx)(g,{...{[v]:""},ref:s,children:r})});return y.displayName=m,[{Provider:s,Slot:d,ItemSlot:y},function(t){let n=u(e+"CollectionConsumer",t);return o.useCallback(()=>{let e=n.collectionRef.current;if(!e)return[];let t=Array.from(e.querySelectorAll("[".concat(v,"]")));return Array.from(n.itemMap.values()).sort((e,n)=>t.indexOf(e.ref.current)-t.indexOf(n.ref.current))},[n.collectionRef,n.itemMap])},r]}var v=new WeakMap;function g(e,t){if("at"in Array.prototype)return Array.prototype.at.call(e,t);let n=function(e,t){let n=e.length,r=y(t),o=r>=0?r:n+r;return o<0||o>=n?-1:o}(e,t);return -1===n?void 0:e[n]}function y(e){return e!=e||0===e?0:Math.trunc(e)}r=new WeakMap,class e extends Map{set(e,t){return v.get(this)&&(this.has(e)?f(this,r)[f(this,r).indexOf(e)]=e:f(this,r).push(e)),super.set(e,t),this}insert(e,t,n){let o,i=this.has(t),a=f(this,r).length,l=y(e),u=l>=0?l:a+l,s=u<0||u>=a?-1:u;if(s===this.size||i&&s===this.size-1||-1===s)return this.set(t,n),this;let c=this.size+ +!i;l<0&&u++;let d=[...f(this,r)],p=!1;for(let e=u;e<c;e++)if(u===e){let r=d[e];d[e]===t&&(r=d[e+1]),i&&this.delete(t),o=this.get(r),this.set(t,n)}else{p||d[e-1]!==t||(p=!0);let n=d[p?e:e-1],r=o;o=this.get(n),this.delete(n),this.set(n,r)}return this}with(t,n,r){let o=new e(this);return o.insert(t,n,r),o}before(e){let t=f(this,r).indexOf(e)-1;if(!(t<0))return this.entryAt(t)}setBefore(e,t,n){let o=f(this,r).indexOf(e);return -1===o?this:this.insert(o,t,n)}after(e){let t=f(this,r).indexOf(e);if(-1!==(t=-1===t||t===this.size-1?-1:t+1))return this.entryAt(t)}setAfter(e,t,n){let o=f(this,r).indexOf(e);return -1===o?this:this.insert(o+1,t,n)}first(){return this.entryAt(0)}last(){return this.entryAt(-1)}clear(){return d(this,r,[]),super.clear()}delete(e){let t=super.delete(e);return t&&f(this,r).splice(f(this,r).indexOf(e),1),t}deleteAt(e){let t=this.keyAt(e);return void 0!==t&&this.delete(t)}at(e){let t=g(f(this,r),e);if(void 0!==t)return this.get(t)}entryAt(e){let t=g(f(this,r),e);if(void 0!==t)return[t,this.get(t)]}indexOf(e){return f(this,r).indexOf(e)}keyAt(e){return g(f(this,r),e)}from(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.at(r)}keyFrom(e,t){let n=this.indexOf(e);if(-1===n)return;let r=n+t;return r<0&&(r=0),r>=this.size&&(r=this.size-1),this.keyAt(r)}find(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return r;n++}}findIndex(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return n;n++}return -1}filter(t,n){let r=[],o=0;for(let e of this)Reflect.apply(t,n,[e,o,this])&&r.push(e),o++;return new e(r)}map(t,n){let r=[],o=0;for(let e of this)r.push([e[0],Reflect.apply(t,n,[e,o,this])]),o++;return new e(r)}reduce(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=0,a=null!=o?o:this.at(0);for(let e of this)a=0===i&&1===t.length?e:Reflect.apply(r,this,[a,e,i,this]),i++;return a}reduceRight(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];let[r,o]=t,i=null!=o?o:this.at(-1);for(let e=this.size-1;e>=0;e--){let n=this.at(e);i=e===this.size-1&&1===t.length?n:Reflect.apply(r,this,[i,n,e,this])}return i}toSorted(t){return new e([...this.entries()].sort(t))}toReversed(){let t=new e;for(let e=this.size-1;e>=0;e--){let n=this.keyAt(e),r=this.get(n);t.set(n,r)}return t}toSpliced(){for(var t=arguments.length,n=Array(t),r=0;r<t;r++)n[r]=arguments[r];let o=[...this.entries()];return o.splice(...n),new e(o)}slice(t,n){let r=new e,o=this.size-1;if(void 0===t)return r;t<0&&(t+=this.size),void 0!==n&&n>0&&(o=n-1);for(let e=t;e<=o;e++){let t=this.keyAt(e),n=this.get(t);r.set(t,n)}return r}every(e,t){let n=0;for(let r of this){if(!Reflect.apply(e,t,[r,n,this]))return!1;n++}return!0}some(e,t){let n=0;for(let r of this){if(Reflect.apply(e,t,[r,n,this]))return!0;n++}return!1}constructor(e){super(e),function(e,t,n){if(t.has(e))throw TypeError("Cannot initialize the same private elements twice on an object");t.set(e,n)}(this,r,{writable:!0,value:void 0}),d(this,r,[...super.keys()]),v.set(this,!0)}};var w=o.createContext(void 0);function b(e){let t=o.useContext(w);return e||t||"ltr"}var x=n(9178),E=n(2293),R=n(7900),C=n(1285);let A=["top","right","bottom","left"],M=Math.min,S=Math.max,k=Math.round,O=Math.floor,P=e=>({x:e,y:e}),N={left:"right",right:"left",bottom:"top",top:"bottom"},j={start:"end",end:"start"};function T(e,t){return"function"==typeof e?e(t):e}function D(e){return e.split("-")[0]}function L(e){return e.split("-")[1]}function _(e){return"x"===e?"y":"x"}function I(e){return"y"===e?"height":"width"}let F=new Set(["top","bottom"]);function W(e){return F.has(D(e))?"y":"x"}function B(e){return e.replace(/start|end/g,e=>j[e])}let U=["left","right"],z=["right","left"],K=["top","bottom"],G=["bottom","top"];function H(e){return e.replace(/left|right|bottom|top/g,e=>N[e])}function V(e){return"number"!=typeof e?{top:0,right:0,bottom:0,left:0,...e}:{top:e,right:e,bottom:e,left:e}}function q(e){let{x:t,y:n,width:r,height:o}=e;return{width:r,height:o,top:n,left:t,right:t+r,bottom:n+o,x:t,y:n}}function X(e,t,n){let r,{reference:o,floating:i}=e,a=W(t),l=_(W(t)),u=I(l),s=D(t),c="y"===a,f=o.x+o.width/2-i.width/2,d=o.y+o.height/2-i.height/2,p=o[u]/2-i[u]/2;switch(s){case"top":r={x:f,y:o.y-i.height};break;case"bottom":r={x:f,y:o.y+o.height};break;case"right":r={x:o.x+o.width,y:d};break;case"left":r={x:o.x-i.width,y:d};break;default:r={x:o.x,y:o.y}}switch(L(t)){case"start":r[l]-=p*(n&&c?-1:1);break;case"end":r[l]+=p*(n&&c?-1:1)}return r}let $=async(e,t,n)=>{let{placement:r="bottom",strategy:o="absolute",middleware:i=[],platform:a}=n,l=i.filter(Boolean),u=await (null==a.isRTL?void 0:a.isRTL(t)),s=await a.getElementRects({reference:e,floating:t,strategy:o}),{x:c,y:f}=X(s,r,u),d=r,p={},h=0;for(let n=0;n<l.length;n++){let{name:i,fn:m}=l[n],{x:v,y:g,data:y,reset:w}=await m({x:c,y:f,initialPlacement:r,placement:d,strategy:o,middlewareData:p,rects:s,platform:a,elements:{reference:e,floating:t}});c=null!=v?v:c,f=null!=g?g:f,p={...p,[i]:{...p[i],...y}},w&&h<=50&&(h++,"object"==typeof w&&(w.placement&&(d=w.placement),w.rects&&(s=!0===w.rects?await a.getElementRects({reference:e,floating:t,strategy:o}):w.rects),{x:c,y:f}=X(s,d,u)),n=-1)}return{x:c,y:f,placement:d,strategy:o,middlewareData:p}};async function Y(e,t){var n;void 0===t&&(t={});let{x:r,y:o,platform:i,rects:a,elements:l,strategy:u}=e,{boundary:s="clippingAncestors",rootBoundary:c="viewport",elementContext:f="floating",altBoundary:d=!1,padding:p=0}=T(t,e),h=V(p),m=l[d?"floating"===f?"reference":"floating":f],v=q(await i.getClippingRect({element:null==(n=await (null==i.isElement?void 0:i.isElement(m)))||n?m:m.contextElement||await (null==i.getDocumentElement?void 0:i.getDocumentElement(l.floating)),boundary:s,rootBoundary:c,strategy:u})),g="floating"===f?{x:r,y:o,width:a.floating.width,height:a.floating.height}:a.reference,y=await (null==i.getOffsetParent?void 0:i.getOffsetParent(l.floating)),w=await (null==i.isElement?void 0:i.isElement(y))&&await (null==i.getScale?void 0:i.getScale(y))||{x:1,y:1},b=q(i.convertOffsetParentRelativeRectToViewportRelativeRect?await i.convertOffsetParentRelativeRectToViewportRelativeRect({elements:l,rect:g,offsetParent:y,strategy:u}):g);return{top:(v.top-b.top+h.top)/w.y,bottom:(b.bottom-v.bottom+h.bottom)/w.y,left:(v.left-b.left+h.left)/w.x,right:(b.right-v.right+h.right)/w.x}}function Z(e,t){return{top:e.top-t.height,right:e.right-t.width,bottom:e.bottom-t.height,left:e.left-t.width}}function J(e){return A.some(t=>e[t]>=0)}let Q=new Set(["left","top"]);async function ee(e,t){let{placement:n,platform:r,elements:o}=e,i=await (null==r.isRTL?void 0:r.isRTL(o.floating)),a=D(n),l=L(n),u="y"===W(n),s=Q.has(a)?-1:1,c=i&&u?-1:1,f=T(t,e),{mainAxis:d,crossAxis:p,alignmentAxis:h}="number"==typeof f?{mainAxis:f,crossAxis:0,alignmentAxis:null}:{mainAxis:f.mainAxis||0,crossAxis:f.crossAxis||0,alignmentAxis:f.alignmentAxis};return l&&"number"==typeof h&&(p="end"===l?-1*h:h),u?{x:p*c,y:d*s}:{x:d*s,y:p*c}}function et(){return"undefined"!=typeof window}function en(e){return ei(e)?(e.nodeName||"").toLowerCase():"#document"}function er(e){var t;return(null==e||null==(t=e.ownerDocument)?void 0:t.defaultView)||window}function eo(e){var t;return null==(t=(ei(e)?e.ownerDocument:e.document)||window.document)?void 0:t.documentElement}function ei(e){return!!et()&&(e instanceof Node||e instanceof er(e).Node)}function ea(e){return!!et()&&(e instanceof Element||e instanceof er(e).Element)}function el(e){return!!et()&&(e instanceof HTMLElement||e instanceof er(e).HTMLElement)}function eu(e){return!!et()&&"undefined"!=typeof ShadowRoot&&(e instanceof ShadowRoot||e instanceof er(e).ShadowRoot)}let es=new Set(["inline","contents"]);function ec(e){let{overflow:t,overflowX:n,overflowY:r,display:o}=ex(e);return/auto|scroll|overlay|hidden|clip/.test(t+r+n)&&!es.has(o)}let ef=new Set(["table","td","th"]),ed=[":popover-open",":modal"];function ep(e){return ed.some(t=>{try{return e.matches(t)}catch(e){return!1}})}let eh=["transform","translate","scale","rotate","perspective"],em=["transform","translate","scale","rotate","perspective","filter"],ev=["paint","layout","strict","content"];function eg(e){let t=ey(),n=ea(e)?ex(e):e;return eh.some(e=>!!n[e]&&"none"!==n[e])||!!n.containerType&&"normal"!==n.containerType||!t&&!!n.backdropFilter&&"none"!==n.backdropFilter||!t&&!!n.filter&&"none"!==n.filter||em.some(e=>(n.willChange||"").includes(e))||ev.some(e=>(n.contain||"").includes(e))}function ey(){return"undefined"!=typeof CSS&&!!CSS.supports&&CSS.supports("-webkit-backdrop-filter","none")}let ew=new Set(["html","body","#document"]);function eb(e){return ew.has(en(e))}function ex(e){return er(e).getComputedStyle(e)}function eE(e){return ea(e)?{scrollLeft:e.scrollLeft,scrollTop:e.scrollTop}:{scrollLeft:e.scrollX,scrollTop:e.scrollY}}function eR(e){if("html"===en(e))return e;let t=e.assignedSlot||e.parentNode||eu(e)&&e.host||eo(e);return eu(t)?t.host:t}function eC(e,t,n){var r;void 0===t&&(t=[]),void 0===n&&(n=!0);let o=function e(t){let n=eR(t);return eb(n)?t.ownerDocument?t.ownerDocument.body:t.body:el(n)&&ec(n)?n:e(n)}(e),i=o===(null==(r=e.ownerDocument)?void 0:r.body),a=er(o);if(i){let e=eA(a);return t.concat(a,a.visualViewport||[],ec(o)?o:[],e&&n?eC(e):[])}return t.concat(o,eC(o,[],n))}function eA(e){return e.parent&&Object.getPrototypeOf(e.parent)?e.frameElement:null}function eM(e){let t=ex(e),n=parseFloat(t.width)||0,r=parseFloat(t.height)||0,o=el(e),i=o?e.offsetWidth:n,a=o?e.offsetHeight:r,l=k(n)!==i||k(r)!==a;return l&&(n=i,r=a),{width:n,height:r,$:l}}function eS(e){return ea(e)?e:e.contextElement}function ek(e){let t=eS(e);if(!el(t))return P(1);let n=t.getBoundingClientRect(),{width:r,height:o,$:i}=eM(t),a=(i?k(n.width):n.width)/r,l=(i?k(n.height):n.height)/o;return a&&Number.isFinite(a)||(a=1),l&&Number.isFinite(l)||(l=1),{x:a,y:l}}let eO=P(0);function eP(e){let t=er(e);return ey()&&t.visualViewport?{x:t.visualViewport.offsetLeft,y:t.visualViewport.offsetTop}:eO}function eN(e,t,n,r){var o;void 0===t&&(t=!1),void 0===n&&(n=!1);let i=e.getBoundingClientRect(),a=eS(e),l=P(1);t&&(r?ea(r)&&(l=ek(r)):l=ek(e));let u=(void 0===(o=n)&&(o=!1),r&&(!o||r===er(a))&&o)?eP(a):P(0),s=(i.left+u.x)/l.x,c=(i.top+u.y)/l.y,f=i.width/l.x,d=i.height/l.y;if(a){let e=er(a),t=r&&ea(r)?er(r):r,n=e,o=eA(n);for(;o&&r&&t!==n;){let e=ek(o),t=o.getBoundingClientRect(),r=ex(o),i=t.left+(o.clientLeft+parseFloat(r.paddingLeft))*e.x,a=t.top+(o.clientTop+parseFloat(r.paddingTop))*e.y;s*=e.x,c*=e.y,f*=e.x,d*=e.y,s+=i,c+=a,o=eA(n=er(o))}}return q({width:f,height:d,x:s,y:c})}function ej(e,t){let n=eE(e).scrollLeft;return t?t.left+n:eN(eo(e)).left+n}function eT(e,t,n){void 0===n&&(n=!1);let r=e.getBoundingClientRect();return{x:r.left+t.scrollLeft-(n?0:ej(e,r)),y:r.top+t.scrollTop}}let eD=new Set(["absolute","fixed"]);function eL(e,t,n){let r;if("viewport"===t)r=function(e,t){let n=er(e),r=eo(e),o=n.visualViewport,i=r.clientWidth,a=r.clientHeight,l=0,u=0;if(o){i=o.width,a=o.height;let e=ey();(!e||e&&"fixed"===t)&&(l=o.offsetLeft,u=o.offsetTop)}return{width:i,height:a,x:l,y:u}}(e,n);else if("document"===t)r=function(e){let t=eo(e),n=eE(e),r=e.ownerDocument.body,o=S(t.scrollWidth,t.clientWidth,r.scrollWidth,r.clientWidth),i=S(t.scrollHeight,t.clientHeight,r.scrollHeight,r.clientHeight),a=-n.scrollLeft+ej(e),l=-n.scrollTop;return"rtl"===ex(r).direction&&(a+=S(t.clientWidth,r.clientWidth)-o),{width:o,height:i,x:a,y:l}}(eo(e));else if(ea(t))r=function(e,t){let n=eN(e,!0,"fixed"===t),r=n.top+e.clientTop,o=n.left+e.clientLeft,i=el(e)?ek(e):P(1),a=e.clientWidth*i.x,l=e.clientHeight*i.y;return{width:a,height:l,x:o*i.x,y:r*i.y}}(t,n);else{let n=eP(e);r={x:t.x-n.x,y:t.y-n.y,width:t.width,height:t.height}}return q(r)}function e_(e){return"static"===ex(e).position}function eI(e,t){if(!el(e)||"fixed"===ex(e).position)return null;if(t)return t(e);let n=e.offsetParent;return eo(e)===n&&(n=n.ownerDocument.body),n}function eF(e,t){var n;let r=er(e);if(ep(e))return r;if(!el(e)){let t=eR(e);for(;t&&!eb(t);){if(ea(t)&&!e_(t))return t;t=eR(t)}return r}let o=eI(e,t);for(;o&&(n=o,ef.has(en(n)))&&e_(o);)o=eI(o,t);return o&&eb(o)&&e_(o)&&!eg(o)?r:o||function(e){let t=eR(e);for(;el(t)&&!eb(t);){if(eg(t))return t;if(ep(t))break;t=eR(t)}return null}(e)||r}let eW=async function(e){let t=this.getOffsetParent||eF,n=this.getDimensions,r=await n(e.floating);return{reference:function(e,t,n){let r=el(t),o=eo(t),i="fixed"===n,a=eN(e,!0,i,t),l={scrollLeft:0,scrollTop:0},u=P(0);if(r||!r&&!i)if(("body"!==en(t)||ec(o))&&(l=eE(t)),r){let e=eN(t,!0,i,t);u.x=e.x+t.clientLeft,u.y=e.y+t.clientTop}else o&&(u.x=ej(o));i&&!r&&o&&(u.x=ej(o));let s=!o||r||i?P(0):eT(o,l);return{x:a.left+l.scrollLeft-u.x-s.x,y:a.top+l.scrollTop-u.y-s.y,width:a.width,height:a.height}}(e.reference,await t(e.floating),e.strategy),floating:{x:0,y:0,width:r.width,height:r.height}}},eB={convertOffsetParentRelativeRectToViewportRelativeRect:function(e){let{elements:t,rect:n,offsetParent:r,strategy:o}=e,i="fixed"===o,a=eo(r),l=!!t&&ep(t.floating);if(r===a||l&&i)return n;let u={scrollLeft:0,scrollTop:0},s=P(1),c=P(0),f=el(r);if((f||!f&&!i)&&(("body"!==en(r)||ec(a))&&(u=eE(r)),el(r))){let e=eN(r);s=ek(r),c.x=e.x+r.clientLeft,c.y=e.y+r.clientTop}let d=!a||f||i?P(0):eT(a,u,!0);return{width:n.width*s.x,height:n.height*s.y,x:n.x*s.x-u.scrollLeft*s.x+c.x+d.x,y:n.y*s.y-u.scrollTop*s.y+c.y+d.y}},getDocumentElement:eo,getClippingRect:function(e){let{element:t,boundary:n,rootBoundary:r,strategy:o}=e,i=[..."clippingAncestors"===n?ep(t)?[]:function(e,t){let n=t.get(e);if(n)return n;let r=eC(e,[],!1).filter(e=>ea(e)&&"body"!==en(e)),o=null,i="fixed"===ex(e).position,a=i?eR(e):e;for(;ea(a)&&!eb(a);){let t=ex(a),n=eg(a);n||"fixed"!==t.position||(o=null),(i?!n&&!o:!n&&"static"===t.position&&!!o&&eD.has(o.position)||ec(a)&&!n&&function e(t,n){let r=eR(t);return!(r===n||!ea(r)||eb(r))&&("fixed"===ex(r).position||e(r,n))}(e,a))?r=r.filter(e=>e!==a):o=t,a=eR(a)}return t.set(e,r),r}(t,this._c):[].concat(n),r],a=i[0],l=i.reduce((e,n)=>{let r=eL(t,n,o);return e.top=S(r.top,e.top),e.right=M(r.right,e.right),e.bottom=M(r.bottom,e.bottom),e.left=S(r.left,e.left),e},eL(t,a,o));return{width:l.right-l.left,height:l.bottom-l.top,x:l.left,y:l.top}},getOffsetParent:eF,getElementRects:eW,getClientRects:function(e){return Array.from(e.getClientRects())},getDimensions:function(e){let{width:t,height:n}=eM(e);return{width:t,height:n}},getScale:ek,isElement:ea,isRTL:function(e){return"rtl"===ex(e).direction}};function eU(e,t){return e.x===t.x&&e.y===t.y&&e.width===t.width&&e.height===t.height}let ez=e=>({name:"arrow",options:e,async fn(t){let{x:n,y:r,placement:o,rects:i,platform:a,elements:l,middlewareData:u}=t,{element:s,padding:c=0}=T(e,t)||{};if(null==s)return{};let f=V(c),d={x:n,y:r},p=_(W(o)),h=I(p),m=await a.getDimensions(s),v="y"===p,g=v?"clientHeight":"clientWidth",y=i.reference[h]+i.reference[p]-d[p]-i.floating[h],w=d[p]-i.reference[p],b=await (null==a.getOffsetParent?void 0:a.getOffsetParent(s)),x=b?b[g]:0;x&&await (null==a.isElement?void 0:a.isElement(b))||(x=l.floating[g]||i.floating[h]);let E=x/2-m[h]/2-1,R=M(f[v?"top":"left"],E),C=M(f[v?"bottom":"right"],E),A=x-m[h]-C,k=x/2-m[h]/2+(y/2-w/2),O=S(R,M(k,A)),P=!u.arrow&&null!=L(o)&&k!==O&&i.reference[h]/2-(k<R?R:C)-m[h]/2<0,N=P?k<R?k-R:k-A:0;return{[p]:d[p]+N,data:{[p]:O,centerOffset:k-O-N,...P&&{alignmentOffset:N}},reset:P}}});var eK=n(7650),eG="undefined"!=typeof document?o.useLayoutEffect:function(){};function eH(e,t){let n,r,o;if(e===t)return!0;if(typeof e!=typeof t)return!1;if("function"==typeof e&&e.toString()===t.toString())return!0;if(e&&t&&"object"==typeof e){if(Array.isArray(e)){if((n=e.length)!==t.length)return!1;for(r=n;0!=r--;)if(!eH(e[r],t[r]))return!1;return!0}if((n=(o=Object.keys(e)).length)!==Object.keys(t).length)return!1;for(r=n;0!=r--;)if(!({}).hasOwnProperty.call(t,o[r]))return!1;for(r=n;0!=r--;){let n=o[r];if(("_owner"!==n||!e.$$typeof)&&!eH(e[n],t[n]))return!1}return!0}return e!=e&&t!=t}function eV(e){return"undefined"==typeof window?1:(e.ownerDocument.defaultView||window).devicePixelRatio||1}function eq(e,t){let n=eV(e);return Math.round(t*n)/n}function eX(e){let t=o.useRef(e);return eG(()=>{t.current=e}),t}var e$=o.forwardRef((e,t)=>{let{children:n,width:r=10,height:o=5,...i}=e;return(0,h.jsx)(s.sG.svg,{...i,ref:t,width:r,height:o,viewBox:"0 0 30 10",preserveAspectRatio:"none",children:e.asChild?n:(0,h.jsx)("polygon",{points:"0,0 30,0 15,10"})})});e$.displayName="Arrow";var eY=n(9033),eZ=n(2712),eJ="Popper",[eQ,e0]=(0,l.A)(eJ),[e1,e2]=eQ(eJ),e5=e=>{let{__scopePopper:t,children:n}=e,[r,i]=o.useState(null);return(0,h.jsx)(e1,{scope:t,anchor:r,onAnchorChange:i,children:n})};e5.displayName=eJ;var e4="PopperAnchor",e3=o.forwardRef((e,t)=>{let{__scopePopper:n,virtualRef:r,...i}=e,l=e2(e4,n),u=o.useRef(null),c=(0,a.s)(t,u);return o.useEffect(()=>{l.onAnchorChange((null==r?void 0:r.current)||u.current)}),r?null:(0,h.jsx)(s.sG.div,{...i,ref:c})});e3.displayName=e4;var e9="PopperContent",[e6,e7]=eQ(e9),e8=o.forwardRef((e,t)=>{var n,r,i,l,u,c,f,d;let{__scopePopper:p,side:m="bottom",sideOffset:v=0,align:g="center",alignOffset:y=0,arrowPadding:w=0,avoidCollisions:b=!0,collisionBoundary:x=[],collisionPadding:E=0,sticky:R="partial",hideWhenDetached:C=!1,updatePositionStrategy:A="optimized",onPlaced:k,...P}=e,N=e2(e9,p),[j,F]=o.useState(null),V=(0,a.s)(t,e=>F(e)),[q,X]=o.useState(null),et=function(e){let[t,n]=o.useState(void 0);return(0,eZ.N)(()=>{if(e){n({width:e.offsetWidth,height:e.offsetHeight});let t=new ResizeObserver(t=>{let r,o;if(!Array.isArray(t)||!t.length)return;let i=t[0];if("borderBoxSize"in i){let e=i.borderBoxSize,t=Array.isArray(e)?e[0]:e;r=t.inlineSize,o=t.blockSize}else r=e.offsetWidth,o=e.offsetHeight;n({width:r,height:o})});return t.observe(e,{box:"border-box"}),()=>t.unobserve(e)}n(void 0)},[e]),t}(q),en=null!=(f=null==et?void 0:et.width)?f:0,er=null!=(d=null==et?void 0:et.height)?d:0,ei="number"==typeof E?E:{top:0,right:0,bottom:0,left:0,...E},ea=Array.isArray(x)?x:[x],el=ea.length>0,eu={padding:ei,boundary:ea.filter(tr),altBoundary:el},{refs:es,floatingStyles:ec,placement:ef,isPositioned:ed,middlewareData:ep}=function(e){void 0===e&&(e={});let{placement:t="bottom",strategy:n="absolute",middleware:r=[],platform:i,elements:{reference:a,floating:l}={},transform:u=!0,whileElementsMounted:s,open:c}=e,[f,d]=o.useState({x:0,y:0,strategy:n,placement:t,middlewareData:{},isPositioned:!1}),[p,h]=o.useState(r);eH(p,r)||h(r);let[m,v]=o.useState(null),[g,y]=o.useState(null),w=o.useCallback(e=>{e!==R.current&&(R.current=e,v(e))},[]),b=o.useCallback(e=>{e!==C.current&&(C.current=e,y(e))},[]),x=a||m,E=l||g,R=o.useRef(null),C=o.useRef(null),A=o.useRef(f),M=null!=s,S=eX(s),k=eX(i),O=eX(c),P=o.useCallback(()=>{if(!R.current||!C.current)return;let e={placement:t,strategy:n,middleware:p};k.current&&(e.platform=k.current),((e,t,n)=>{let r=new Map,o={platform:eB,...n},i={...o.platform,_c:r};return $(e,t,{...o,platform:i})})(R.current,C.current,e).then(e=>{let t={...e,isPositioned:!1!==O.current};N.current&&!eH(A.current,t)&&(A.current=t,eK.flushSync(()=>{d(t)}))})},[p,t,n,k,O]);eG(()=>{!1===c&&A.current.isPositioned&&(A.current.isPositioned=!1,d(e=>({...e,isPositioned:!1})))},[c]);let N=o.useRef(!1);eG(()=>(N.current=!0,()=>{N.current=!1}),[]),eG(()=>{if(x&&(R.current=x),E&&(C.current=E),x&&E){if(S.current)return S.current(x,E,P);P()}},[x,E,P,S,M]);let j=o.useMemo(()=>({reference:R,floating:C,setReference:w,setFloating:b}),[w,b]),T=o.useMemo(()=>({reference:x,floating:E}),[x,E]),D=o.useMemo(()=>{let e={position:n,left:0,top:0};if(!T.floating)return e;let t=eq(T.floating,f.x),r=eq(T.floating,f.y);return u?{...e,transform:"translate("+t+"px, "+r+"px)",...eV(T.floating)>=1.5&&{willChange:"transform"}}:{position:n,left:t,top:r}},[n,u,T.floating,f.x,f.y]);return o.useMemo(()=>({...f,update:P,refs:j,elements:T,floatingStyles:D}),[f,P,j,T,D])}({strategy:"fixed",placement:m+("center"!==g?"-"+g:""),whileElementsMounted:function(){for(var e=arguments.length,t=Array(e),n=0;n<e;n++)t[n]=arguments[n];return function(e,t,n,r){let o;void 0===r&&(r={});let{ancestorScroll:i=!0,ancestorResize:a=!0,elementResize:l="function"==typeof ResizeObserver,layoutShift:u="function"==typeof IntersectionObserver,animationFrame:s=!1}=r,c=eS(e),f=i||a?[...c?eC(c):[],...eC(t)]:[];f.forEach(e=>{i&&e.addEventListener("scroll",n,{passive:!0}),a&&e.addEventListener("resize",n)});let d=c&&u?function(e,t){let n,r=null,o=eo(e);function i(){var e;clearTimeout(n),null==(e=r)||e.disconnect(),r=null}return!function a(l,u){void 0===l&&(l=!1),void 0===u&&(u=1),i();let s=e.getBoundingClientRect(),{left:c,top:f,width:d,height:p}=s;if(l||t(),!d||!p)return;let h=O(f),m=O(o.clientWidth-(c+d)),v={rootMargin:-h+"px "+-m+"px "+-O(o.clientHeight-(f+p))+"px "+-O(c)+"px",threshold:S(0,M(1,u))||1},g=!0;function y(t){let r=t[0].intersectionRatio;if(r!==u){if(!g)return a();r?a(!1,r):n=setTimeout(()=>{a(!1,1e-7)},1e3)}1!==r||eU(s,e.getBoundingClientRect())||a(),g=!1}try{r=new IntersectionObserver(y,{...v,root:o.ownerDocument})}catch(e){r=new IntersectionObserver(y,v)}r.observe(e)}(!0),i}(c,n):null,p=-1,h=null;l&&(h=new ResizeObserver(e=>{let[r]=e;r&&r.target===c&&h&&(h.unobserve(t),cancelAnimationFrame(p),p=requestAnimationFrame(()=>{var e;null==(e=h)||e.observe(t)})),n()}),c&&!s&&h.observe(c),h.observe(t));let m=s?eN(e):null;return s&&function t(){let r=eN(e);m&&!eU(m,r)&&n(),m=r,o=requestAnimationFrame(t)}(),n(),()=>{var e;f.forEach(e=>{i&&e.removeEventListener("scroll",n),a&&e.removeEventListener("resize",n)}),null==d||d(),null==(e=h)||e.disconnect(),h=null,s&&cancelAnimationFrame(o)}}(...t,{animationFrame:"always"===A})},elements:{reference:N.anchor},middleware:[((e,t)=>({...function(e){return void 0===e&&(e=0),{name:"offset",options:e,async fn(t){var n,r;let{x:o,y:i,placement:a,middlewareData:l}=t,u=await ee(t,e);return a===(null==(n=l.offset)?void 0:n.placement)&&null!=(r=l.arrow)&&r.alignmentOffset?{}:{x:o+u.x,y:i+u.y,data:{...u,placement:a}}}}}(e),options:[e,t]}))({mainAxis:v+er,alignmentAxis:y}),b&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"shift",options:e,async fn(t){let{x:n,y:r,placement:o}=t,{mainAxis:i=!0,crossAxis:a=!1,limiter:l={fn:e=>{let{x:t,y:n}=e;return{x:t,y:n}}},...u}=T(e,t),s={x:n,y:r},c=await Y(t,u),f=W(D(o)),d=_(f),p=s[d],h=s[f];if(i){let e="y"===d?"top":"left",t="y"===d?"bottom":"right",n=p+c[e],r=p-c[t];p=S(n,M(p,r))}if(a){let e="y"===f?"top":"left",t="y"===f?"bottom":"right",n=h+c[e],r=h-c[t];h=S(n,M(h,r))}let m=l.fn({...t,[d]:p,[f]:h});return{...m,data:{x:m.x-n,y:m.y-r,enabled:{[d]:i,[f]:a}}}}}}(e),options:[e,t]}))({mainAxis:!0,crossAxis:!1,limiter:"partial"===R?((e,t)=>({...function(e){return void 0===e&&(e={}),{options:e,fn(t){let{x:n,y:r,placement:o,rects:i,middlewareData:a}=t,{offset:l=0,mainAxis:u=!0,crossAxis:s=!0}=T(e,t),c={x:n,y:r},f=W(o),d=_(f),p=c[d],h=c[f],m=T(l,t),v="number"==typeof m?{mainAxis:m,crossAxis:0}:{mainAxis:0,crossAxis:0,...m};if(u){let e="y"===d?"height":"width",t=i.reference[d]-i.floating[e]+v.mainAxis,n=i.reference[d]+i.reference[e]-v.mainAxis;p<t?p=t:p>n&&(p=n)}if(s){var g,y;let e="y"===d?"width":"height",t=Q.has(D(o)),n=i.reference[f]-i.floating[e]+(t&&(null==(g=a.offset)?void 0:g[f])||0)+(t?0:v.crossAxis),r=i.reference[f]+i.reference[e]+(t?0:(null==(y=a.offset)?void 0:y[f])||0)-(t?v.crossAxis:0);h<n?h=n:h>r&&(h=r)}return{[d]:p,[f]:h}}}}(e),options:[e,t]}))():void 0,...eu}),b&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"flip",options:e,async fn(t){var n,r,o,i,a;let{placement:l,middlewareData:u,rects:s,initialPlacement:c,platform:f,elements:d}=t,{mainAxis:p=!0,crossAxis:h=!0,fallbackPlacements:m,fallbackStrategy:v="bestFit",fallbackAxisSideDirection:g="none",flipAlignment:y=!0,...w}=T(e,t);if(null!=(n=u.arrow)&&n.alignmentOffset)return{};let b=D(l),x=W(c),E=D(c)===c,R=await (null==f.isRTL?void 0:f.isRTL(d.floating)),C=m||(E||!y?[H(c)]:function(e){let t=H(e);return[B(e),t,B(t)]}(c)),A="none"!==g;!m&&A&&C.push(...function(e,t,n,r){let o=L(e),i=function(e,t,n){switch(e){case"top":case"bottom":if(n)return t?z:U;return t?U:z;case"left":case"right":return t?K:G;default:return[]}}(D(e),"start"===n,r);return o&&(i=i.map(e=>e+"-"+o),t&&(i=i.concat(i.map(B)))),i}(c,y,g,R));let M=[c,...C],S=await Y(t,w),k=[],O=(null==(r=u.flip)?void 0:r.overflows)||[];if(p&&k.push(S[b]),h){let e=function(e,t,n){void 0===n&&(n=!1);let r=L(e),o=_(W(e)),i=I(o),a="x"===o?r===(n?"end":"start")?"right":"left":"start"===r?"bottom":"top";return t.reference[i]>t.floating[i]&&(a=H(a)),[a,H(a)]}(l,s,R);k.push(S[e[0]],S[e[1]])}if(O=[...O,{placement:l,overflows:k}],!k.every(e=>e<=0)){let e=((null==(o=u.flip)?void 0:o.index)||0)+1,t=M[e];if(t&&("alignment"!==h||x===W(t)||O.every(e=>W(e.placement)!==x||e.overflows[0]>0)))return{data:{index:e,overflows:O},reset:{placement:t}};let n=null==(i=O.filter(e=>e.overflows[0]<=0).sort((e,t)=>e.overflows[1]-t.overflows[1])[0])?void 0:i.placement;if(!n)switch(v){case"bestFit":{let e=null==(a=O.filter(e=>{if(A){let t=W(e.placement);return t===x||"y"===t}return!0}).map(e=>[e.placement,e.overflows.filter(e=>e>0).reduce((e,t)=>e+t,0)]).sort((e,t)=>e[1]-t[1])[0])?void 0:a[0];e&&(n=e);break}case"initialPlacement":n=c}if(l!==n)return{reset:{placement:n}}}return{}}}}(e),options:[e,t]}))({...eu}),((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"size",options:e,async fn(t){var n,r;let o,i,{placement:a,rects:l,platform:u,elements:s}=t,{apply:c=()=>{},...f}=T(e,t),d=await Y(t,f),p=D(a),h=L(a),m="y"===W(a),{width:v,height:g}=l.floating;"top"===p||"bottom"===p?(o=p,i=h===(await (null==u.isRTL?void 0:u.isRTL(s.floating))?"start":"end")?"left":"right"):(i=p,o="end"===h?"top":"bottom");let y=g-d.top-d.bottom,w=v-d.left-d.right,b=M(g-d[o],y),x=M(v-d[i],w),E=!t.middlewareData.shift,R=b,C=x;if(null!=(n=t.middlewareData.shift)&&n.enabled.x&&(C=w),null!=(r=t.middlewareData.shift)&&r.enabled.y&&(R=y),E&&!h){let e=S(d.left,0),t=S(d.right,0),n=S(d.top,0),r=S(d.bottom,0);m?C=v-2*(0!==e||0!==t?e+t:S(d.left,d.right)):R=g-2*(0!==n||0!==r?n+r:S(d.top,d.bottom))}await c({...t,availableWidth:C,availableHeight:R});let A=await u.getDimensions(s.floating);return v!==A.width||g!==A.height?{reset:{rects:!0}}:{}}}}(e),options:[e,t]}))({...eu,apply:e=>{let{elements:t,rects:n,availableWidth:r,availableHeight:o}=e,{width:i,height:a}=n.reference,l=t.floating.style;l.setProperty("--radix-popper-available-width","".concat(r,"px")),l.setProperty("--radix-popper-available-height","".concat(o,"px")),l.setProperty("--radix-popper-anchor-width","".concat(i,"px")),l.setProperty("--radix-popper-anchor-height","".concat(a,"px"))}}),q&&((e,t)=>({...(e=>({name:"arrow",options:e,fn(t){let{element:n,padding:r}="function"==typeof e?e(t):e;return n&&({}).hasOwnProperty.call(n,"current")?null!=n.current?ez({element:n.current,padding:r}).fn(t):{}:n?ez({element:n,padding:r}).fn(t):{}}}))(e),options:[e,t]}))({element:q,padding:w}),to({arrowWidth:en,arrowHeight:er}),C&&((e,t)=>({...function(e){return void 0===e&&(e={}),{name:"hide",options:e,async fn(t){let{rects:n}=t,{strategy:r="referenceHidden",...o}=T(e,t);switch(r){case"referenceHidden":{let e=Z(await Y(t,{...o,elementContext:"reference"}),n.reference);return{data:{referenceHiddenOffsets:e,referenceHidden:J(e)}}}case"escaped":{let e=Z(await Y(t,{...o,altBoundary:!0}),n.floating);return{data:{escapedOffsets:e,escaped:J(e)}}}default:return{}}}}}(e),options:[e,t]}))({strategy:"referenceHidden",...eu})]}),[eh,em]=ti(ef),ev=(0,eY.c)(k);(0,eZ.N)(()=>{ed&&(null==ev||ev())},[ed,ev]);let eg=null==(n=ep.arrow)?void 0:n.x,ey=null==(r=ep.arrow)?void 0:r.y,ew=(null==(i=ep.arrow)?void 0:i.centerOffset)!==0,[eb,ex]=o.useState();return(0,eZ.N)(()=>{j&&ex(window.getComputedStyle(j).zIndex)},[j]),(0,h.jsx)("div",{ref:es.setFloating,"data-radix-popper-content-wrapper":"",style:{...ec,transform:ed?ec.transform:"translate(0, -200%)",minWidth:"max-content",zIndex:eb,"--radix-popper-transform-origin":[null==(l=ep.transformOrigin)?void 0:l.x,null==(u=ep.transformOrigin)?void 0:u.y].join(" "),...(null==(c=ep.hide)?void 0:c.referenceHidden)&&{visibility:"hidden",pointerEvents:"none"}},dir:e.dir,children:(0,h.jsx)(e6,{scope:p,placedSide:eh,onArrowChange:X,arrowX:eg,arrowY:ey,shouldHideArrow:ew,children:(0,h.jsx)(s.sG.div,{"data-side":eh,"data-align":em,...P,ref:V,style:{...P.style,animation:ed?void 0:"none"}})})})});e8.displayName=e9;var te="PopperArrow",tt={top:"bottom",right:"left",bottom:"top",left:"right"},tn=o.forwardRef(function(e,t){let{__scopePopper:n,...r}=e,o=e7(te,n),i=tt[o.placedSide];return(0,h.jsx)("span",{ref:o.onArrowChange,style:{position:"absolute",left:o.arrowX,top:o.arrowY,[i]:0,transformOrigin:{top:"",right:"0 0",bottom:"center 0",left:"100% 0"}[o.placedSide],transform:{top:"translateY(100%)",right:"translateY(50%) rotate(90deg) translateX(-50%)",bottom:"rotate(180deg)",left:"translateY(50%) rotate(-90deg) translateX(50%)"}[o.placedSide],visibility:o.shouldHideArrow?"hidden":void 0},children:(0,h.jsx)(e$,{...r,ref:t,style:{...r.style,display:"block"}})})});function tr(e){return null!==e}tn.displayName=te;var to=e=>({name:"transformOrigin",options:e,fn(t){var n,r,o,i,a;let{placement:l,rects:u,middlewareData:s}=t,c=(null==(n=s.arrow)?void 0:n.centerOffset)!==0,f=c?0:e.arrowWidth,d=c?0:e.arrowHeight,[p,h]=ti(l),m={start:"0%",center:"50%",end:"100%"}[h],v=(null!=(i=null==(r=s.arrow)?void 0:r.x)?i:0)+f/2,g=(null!=(a=null==(o=s.arrow)?void 0:o.y)?a:0)+d/2,y="",w="";return"bottom"===p?(y=c?m:"".concat(v,"px"),w="".concat(-d,"px")):"top"===p?(y=c?m:"".concat(v,"px"),w="".concat(u.floating.height+d,"px")):"right"===p?(y="".concat(-d,"px"),w=c?m:"".concat(g,"px")):"left"===p&&(y="".concat(u.floating.width+d,"px"),w=c?m:"".concat(g,"px")),{data:{x:y,y:w}}}});function ti(e){let[t,n="center"]=e.split("-");return[t,n]}var ta=n(4378),tl=n(8905),tu="rovingFocusGroup.onEntryFocus",ts={bubbles:!1,cancelable:!0},tc="RovingFocusGroup",[tf,td,tp]=m(tc),[th,tm]=(0,l.A)(tc,[tp]),[tv,tg]=th(tc),ty=o.forwardRef((e,t)=>(0,h.jsx)(tf.Provider,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(tf.Slot,{scope:e.__scopeRovingFocusGroup,children:(0,h.jsx)(tw,{...e,ref:t})})}));ty.displayName=tc;var tw=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,orientation:r,loop:l=!1,dir:c,currentTabStopId:f,defaultCurrentTabStopId:d,onCurrentTabStopIdChange:p,onEntryFocus:m,preventScrollOnEntryFocus:v=!1,...g}=e,y=o.useRef(null),w=(0,a.s)(t,y),x=b(c),[E,R]=(0,u.i)({prop:f,defaultProp:null!=d?d:null,onChange:p,caller:tc}),[C,A]=o.useState(!1),M=(0,eY.c)(m),S=td(n),k=o.useRef(!1),[O,P]=o.useState(0);return o.useEffect(()=>{let e=y.current;if(e)return e.addEventListener(tu,M),()=>e.removeEventListener(tu,M)},[M]),(0,h.jsx)(tv,{scope:n,orientation:r,dir:x,loop:l,currentTabStopId:E,onItemFocus:o.useCallback(e=>R(e),[R]),onItemShiftTab:o.useCallback(()=>A(!0),[]),onFocusableItemAdd:o.useCallback(()=>P(e=>e+1),[]),onFocusableItemRemove:o.useCallback(()=>P(e=>e-1),[]),children:(0,h.jsx)(s.sG.div,{tabIndex:C||0===O?-1:0,"data-orientation":r,...g,ref:w,style:{outline:"none",...e.style},onMouseDown:(0,i.m)(e.onMouseDown,()=>{k.current=!0}),onFocus:(0,i.m)(e.onFocus,e=>{let t=!k.current;if(e.target===e.currentTarget&&t&&!C){let t=new CustomEvent(tu,ts);if(e.currentTarget.dispatchEvent(t),!t.defaultPrevented){let e=S().filter(e=>e.focusable);tR([e.find(e=>e.active),e.find(e=>e.id===E),...e].filter(Boolean).map(e=>e.ref.current),v)}}k.current=!1}),onBlur:(0,i.m)(e.onBlur,()=>A(!1))})})}),tb="RovingFocusGroupItem",tx=o.forwardRef((e,t)=>{let{__scopeRovingFocusGroup:n,focusable:r=!0,active:a=!1,tabStopId:l,children:u,...c}=e,f=(0,C.B)(),d=l||f,p=tg(tb,n),m=p.currentTabStopId===d,v=td(n),{onFocusableItemAdd:g,onFocusableItemRemove:y,currentTabStopId:w}=p;return o.useEffect(()=>{if(r)return g(),()=>y()},[r,g,y]),(0,h.jsx)(tf.ItemSlot,{scope:n,id:d,focusable:r,active:a,children:(0,h.jsx)(s.sG.span,{tabIndex:m?0:-1,"data-orientation":p.orientation,...c,ref:t,onMouseDown:(0,i.m)(e.onMouseDown,e=>{r?p.onItemFocus(d):e.preventDefault()}),onFocus:(0,i.m)(e.onFocus,()=>p.onItemFocus(d)),onKeyDown:(0,i.m)(e.onKeyDown,e=>{if("Tab"===e.key&&e.shiftKey)return void p.onItemShiftTab();if(e.target!==e.currentTarget)return;let t=function(e,t,n){var r;let o=(r=e.key,"rtl"!==n?r:"ArrowLeft"===r?"ArrowRight":"ArrowRight"===r?"ArrowLeft":r);if(!("vertical"===t&&["ArrowLeft","ArrowRight"].includes(o))&&!("horizontal"===t&&["ArrowUp","ArrowDown"].includes(o)))return tE[o]}(e,p.orientation,p.dir);if(void 0!==t){if(e.metaKey||e.ctrlKey||e.altKey||e.shiftKey)return;e.preventDefault();let n=v().filter(e=>e.focusable).map(e=>e.ref.current);if("last"===t)n.reverse();else if("prev"===t||"next"===t){"prev"===t&&n.reverse();let r=n.indexOf(e.currentTarget);n=p.loop?function(e,t){return e.map((n,r)=>e[(t+r)%e.length])}(n,r+1):n.slice(r+1)}setTimeout(()=>tR(n))}}),children:"function"==typeof u?u({isCurrentTabStop:m,hasTabStop:null!=w}):u})})});tx.displayName=tb;var tE={ArrowLeft:"prev",ArrowUp:"prev",ArrowRight:"next",ArrowDown:"next",PageUp:"first",Home:"first",PageDown:"last",End:"last"};function tR(e){let t=arguments.length>1&&void 0!==arguments[1]&&arguments[1],n=document.activeElement;for(let r of e)if(r===n||(r.focus({preventScroll:t}),document.activeElement!==n))return}var tC=n(8168),tA=n(3795),tM=["Enter"," "],tS=["ArrowUp","PageDown","End"],tk=["ArrowDown","PageUp","Home",...tS],tO={ltr:[...tM,"ArrowRight"],rtl:[...tM,"ArrowLeft"]},tP={ltr:["ArrowLeft"],rtl:["ArrowRight"]},tN="Menu",[tj,tT,tD]=m(tN),[tL,t_]=(0,l.A)(tN,[tD,e0,tm]),tI=e0(),tF=tm(),[tW,tB]=tL(tN),[tU,tz]=tL(tN),tK=e=>{let{__scopeMenu:t,open:n=!1,children:r,dir:i,onOpenChange:a,modal:l=!0}=e,u=tI(t),[s,c]=o.useState(null),f=o.useRef(!1),d=(0,eY.c)(a),p=b(i);return o.useEffect(()=>{let e=()=>{f.current=!0,document.addEventListener("pointerdown",t,{capture:!0,once:!0}),document.addEventListener("pointermove",t,{capture:!0,once:!0})},t=()=>f.current=!1;return document.addEventListener("keydown",e,{capture:!0}),()=>{document.removeEventListener("keydown",e,{capture:!0}),document.removeEventListener("pointerdown",t,{capture:!0}),document.removeEventListener("pointermove",t,{capture:!0})}},[]),(0,h.jsx)(e5,{...u,children:(0,h.jsx)(tW,{scope:t,open:n,onOpenChange:d,content:s,onContentChange:c,children:(0,h.jsx)(tU,{scope:t,onClose:o.useCallback(()=>d(!1),[d]),isUsingKeyboardRef:f,dir:p,modal:l,children:r})})})};tK.displayName=tN;var tG=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=tI(n);return(0,h.jsx)(e3,{...o,...r,ref:t})});tG.displayName="MenuAnchor";var tH="MenuPortal",[tV,tq]=tL(tH,{forceMount:void 0}),tX=e=>{let{__scopeMenu:t,forceMount:n,children:r,container:o}=e,i=tB(tH,t);return(0,h.jsx)(tV,{scope:t,forceMount:n,children:(0,h.jsx)(tl.C,{present:n||i.open,children:(0,h.jsx)(ta.Z,{asChild:!0,container:o,children:r})})})};tX.displayName=tH;var t$="MenuContent",[tY,tZ]=tL(t$),tJ=o.forwardRef((e,t)=>{let n=tq(t$,e.__scopeMenu),{forceMount:r=n.forceMount,...o}=e,i=tB(t$,e.__scopeMenu),a=tz(t$,e.__scopeMenu);return(0,h.jsx)(tj.Provider,{scope:e.__scopeMenu,children:(0,h.jsx)(tl.C,{present:r||i.open,children:(0,h.jsx)(tj.Slot,{scope:e.__scopeMenu,children:a.modal?(0,h.jsx)(tQ,{...o,ref:t}):(0,h.jsx)(t0,{...o,ref:t})})})})}),tQ=o.forwardRef((e,t)=>{let n=tB(t$,e.__scopeMenu),r=o.useRef(null),l=(0,a.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return(0,tC.Eq)(e)},[]),(0,h.jsx)(t2,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:n.open,disableOutsideScroll:!0,onFocusOutside:(0,i.m)(e.onFocusOutside,e=>e.preventDefault(),{checkForDefaultPrevented:!1}),onDismiss:()=>n.onOpenChange(!1)})}),t0=o.forwardRef((e,t)=>{let n=tB(t$,e.__scopeMenu);return(0,h.jsx)(t2,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,disableOutsideScroll:!1,onDismiss:()=>n.onOpenChange(!1)})}),t1=(0,p.TL)("MenuContent.ScrollLock"),t2=o.forwardRef((e,t)=>{let{__scopeMenu:n,loop:r=!1,trapFocus:l,onOpenAutoFocus:u,onCloseAutoFocus:s,disableOutsidePointerEvents:c,onEntryFocus:f,onEscapeKeyDown:d,onPointerDownOutside:p,onFocusOutside:m,onInteractOutside:v,onDismiss:g,disableOutsideScroll:y,...w}=e,b=tB(t$,n),C=tz(t$,n),A=tI(n),M=tF(n),S=tT(n),[k,O]=o.useState(null),P=o.useRef(null),N=(0,a.s)(t,P,b.onContentChange),j=o.useRef(0),T=o.useRef(""),D=o.useRef(0),L=o.useRef(null),_=o.useRef("right"),I=o.useRef(0),F=y?tA.A:o.Fragment;o.useEffect(()=>()=>window.clearTimeout(j.current),[]),(0,E.Oh)();let W=o.useCallback(e=>{var t,n;return _.current===(null==(t=L.current)?void 0:t.side)&&function(e,t){return!!t&&function(e,t){let{x:n,y:r}=e,o=!1;for(let e=0,i=t.length-1;e<t.length;i=e++){let a=t[e],l=t[i],u=a.x,s=a.y,c=l.x,f=l.y;s>r!=f>r&&n<(c-u)*(r-s)/(f-s)+u&&(o=!o)}return o}({x:e.clientX,y:e.clientY},t)}(e,null==(n=L.current)?void 0:n.area)},[]);return(0,h.jsx)(tY,{scope:n,searchRef:T,onItemEnter:o.useCallback(e=>{W(e)&&e.preventDefault()},[W]),onItemLeave:o.useCallback(e=>{var t;W(e)||(null==(t=P.current)||t.focus(),O(null))},[W]),onTriggerLeave:o.useCallback(e=>{W(e)&&e.preventDefault()},[W]),pointerGraceTimerRef:D,onPointerGraceIntentChange:o.useCallback(e=>{L.current=e},[]),children:(0,h.jsx)(F,{...y?{as:t1,allowPinchZoom:!0}:void 0,children:(0,h.jsx)(R.n,{asChild:!0,trapped:l,onMountAutoFocus:(0,i.m)(u,e=>{var t;e.preventDefault(),null==(t=P.current)||t.focus({preventScroll:!0})}),onUnmountAutoFocus:s,children:(0,h.jsx)(x.qW,{asChild:!0,disableOutsidePointerEvents:c,onEscapeKeyDown:d,onPointerDownOutside:p,onFocusOutside:m,onInteractOutside:v,onDismiss:g,children:(0,h.jsx)(ty,{asChild:!0,...M,dir:C.dir,orientation:"vertical",loop:r,currentTabStopId:k,onCurrentTabStopIdChange:O,onEntryFocus:(0,i.m)(f,e=>{C.isUsingKeyboardRef.current||e.preventDefault()}),preventScrollOnEntryFocus:!0,children:(0,h.jsx)(e8,{role:"menu","aria-orientation":"vertical","data-state":ny(b.open),"data-radix-menu-content":"",dir:C.dir,...A,...w,ref:N,style:{outline:"none",...w.style},onKeyDown:(0,i.m)(w.onKeyDown,e=>{let t=e.target.closest("[data-radix-menu-content]")===e.currentTarget,n=e.ctrlKey||e.altKey||e.metaKey,r=1===e.key.length;t&&("Tab"===e.key&&e.preventDefault(),!n&&r&&(e=>{var t,n;let r=T.current+e,o=S().filter(e=>!e.disabled),i=document.activeElement,a=null==(t=o.find(e=>e.ref.current===i))?void 0:t.textValue,l=function(e,t,n){var r;let o=t.length>1&&Array.from(t).every(e=>e===t[0])?t[0]:t,i=n?e.indexOf(n):-1,a=(r=Math.max(i,0),e.map((t,n)=>e[(r+n)%e.length]));1===o.length&&(a=a.filter(e=>e!==n));let l=a.find(e=>e.toLowerCase().startsWith(o.toLowerCase()));return l!==n?l:void 0}(o.map(e=>e.textValue),r,a),u=null==(n=o.find(e=>e.textValue===l))?void 0:n.ref.current;!function e(t){T.current=t,window.clearTimeout(j.current),""!==t&&(j.current=window.setTimeout(()=>e(""),1e3))}(r),u&&setTimeout(()=>u.focus())})(e.key));let o=P.current;if(e.target!==o||!tk.includes(e.key))return;e.preventDefault();let i=S().filter(e=>!e.disabled).map(e=>e.ref.current);tS.includes(e.key)&&i.reverse(),function(e){let t=document.activeElement;for(let n of e)if(n===t||(n.focus(),document.activeElement!==t))return}(i)}),onBlur:(0,i.m)(e.onBlur,e=>{e.currentTarget.contains(e.target)||(window.clearTimeout(j.current),T.current="")}),onPointerMove:(0,i.m)(e.onPointerMove,nx(e=>{let t=e.target,n=I.current!==e.clientX;e.currentTarget.contains(t)&&n&&(_.current=e.clientX>I.current?"right":"left",I.current=e.clientX)}))})})})})})})});tJ.displayName=t$;var t5=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,h.jsx)(s.sG.div,{role:"group",...r,ref:t})});t5.displayName="MenuGroup";var t4=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,h.jsx)(s.sG.div,{...r,ref:t})});t4.displayName="MenuLabel";var t3="MenuItem",t9="menu.itemSelect",t6=o.forwardRef((e,t)=>{let{disabled:n=!1,onSelect:r,...l}=e,u=o.useRef(null),c=tz(t3,e.__scopeMenu),f=tZ(t3,e.__scopeMenu),d=(0,a.s)(t,u),p=o.useRef(!1);return(0,h.jsx)(t7,{...l,ref:d,disabled:n,onClick:(0,i.m)(e.onClick,()=>{let e=u.current;if(!n&&e){let t=new CustomEvent(t9,{bubbles:!0,cancelable:!0});e.addEventListener(t9,e=>null==r?void 0:r(e),{once:!0}),(0,s.hO)(e,t),t.defaultPrevented?p.current=!1:c.onClose()}}),onPointerDown:t=>{var n;null==(n=e.onPointerDown)||n.call(e,t),p.current=!0},onPointerUp:(0,i.m)(e.onPointerUp,e=>{var t;p.current||null==(t=e.currentTarget)||t.click()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=""!==f.searchRef.current;n||t&&" "===e.key||tM.includes(e.key)&&(e.currentTarget.click(),e.preventDefault())})})});t6.displayName=t3;var t7=o.forwardRef((e,t)=>{let{__scopeMenu:n,disabled:r=!1,textValue:l,...u}=e,c=tZ(t3,n),f=tF(n),d=o.useRef(null),p=(0,a.s)(t,d),[m,v]=o.useState(!1),[g,y]=o.useState("");return o.useEffect(()=>{let e=d.current;if(e){var t;y((null!=(t=e.textContent)?t:"").trim())}},[u.children]),(0,h.jsx)(tj.ItemSlot,{scope:n,disabled:r,textValue:null!=l?l:g,children:(0,h.jsx)(tx,{asChild:!0,...f,focusable:!r,children:(0,h.jsx)(s.sG.div,{role:"menuitem","data-highlighted":m?"":void 0,"aria-disabled":r||void 0,"data-disabled":r?"":void 0,...u,ref:p,onPointerMove:(0,i.m)(e.onPointerMove,nx(e=>{r?c.onItemLeave(e):(c.onItemEnter(e),e.defaultPrevented||e.currentTarget.focus({preventScroll:!0}))})),onPointerLeave:(0,i.m)(e.onPointerLeave,nx(e=>c.onItemLeave(e))),onFocus:(0,i.m)(e.onFocus,()=>v(!0)),onBlur:(0,i.m)(e.onBlur,()=>v(!1))})})})}),t8=o.forwardRef((e,t)=>{let{checked:n=!1,onCheckedChange:r,...o}=e;return(0,h.jsx)(nl,{scope:e.__scopeMenu,checked:n,children:(0,h.jsx)(t6,{role:"menuitemcheckbox","aria-checked":nw(n)?"mixed":n,...o,ref:t,"data-state":nb(n),onSelect:(0,i.m)(o.onSelect,()=>null==r?void 0:r(!!nw(n)||!n),{checkForDefaultPrevented:!1})})})});t8.displayName="MenuCheckboxItem";var ne="MenuRadioGroup",[nt,nn]=tL(ne,{value:void 0,onValueChange:()=>{}}),nr=o.forwardRef((e,t)=>{let{value:n,onValueChange:r,...o}=e,i=(0,eY.c)(r);return(0,h.jsx)(nt,{scope:e.__scopeMenu,value:n,onValueChange:i,children:(0,h.jsx)(t5,{...o,ref:t})})});nr.displayName=ne;var no="MenuRadioItem",ni=o.forwardRef((e,t)=>{let{value:n,...r}=e,o=nn(no,e.__scopeMenu),a=n===o.value;return(0,h.jsx)(nl,{scope:e.__scopeMenu,checked:a,children:(0,h.jsx)(t6,{role:"menuitemradio","aria-checked":a,...r,ref:t,"data-state":nb(a),onSelect:(0,i.m)(r.onSelect,()=>{var e;return null==(e=o.onValueChange)?void 0:e.call(o,n)},{checkForDefaultPrevented:!1})})})});ni.displayName=no;var na="MenuItemIndicator",[nl,nu]=tL(na,{checked:!1}),ns=o.forwardRef((e,t)=>{let{__scopeMenu:n,forceMount:r,...o}=e,i=nu(na,n);return(0,h.jsx)(tl.C,{present:r||nw(i.checked)||!0===i.checked,children:(0,h.jsx)(s.sG.span,{...o,ref:t,"data-state":nb(i.checked)})})});ns.displayName=na;var nc=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e;return(0,h.jsx)(s.sG.div,{role:"separator","aria-orientation":"horizontal",...r,ref:t})});nc.displayName="MenuSeparator";var nf=o.forwardRef((e,t)=>{let{__scopeMenu:n,...r}=e,o=tI(n);return(0,h.jsx)(tn,{...o,...r,ref:t})});nf.displayName="MenuArrow";var[nd,np]=tL("MenuSub"),nh="MenuSubTrigger",nm=o.forwardRef((e,t)=>{let n=tB(nh,e.__scopeMenu),r=tz(nh,e.__scopeMenu),l=np(nh,e.__scopeMenu),u=tZ(nh,e.__scopeMenu),s=o.useRef(null),{pointerGraceTimerRef:c,onPointerGraceIntentChange:f}=u,d={__scopeMenu:e.__scopeMenu},p=o.useCallback(()=>{s.current&&window.clearTimeout(s.current),s.current=null},[]);return o.useEffect(()=>p,[p]),o.useEffect(()=>{let e=c.current;return()=>{window.clearTimeout(e),f(null)}},[c,f]),(0,h.jsx)(tG,{asChild:!0,...d,children:(0,h.jsx)(t7,{id:l.triggerId,"aria-haspopup":"menu","aria-expanded":n.open,"aria-controls":l.contentId,"data-state":ny(n.open),...e,ref:(0,a.t)(t,l.onTriggerChange),onClick:t=>{var r;null==(r=e.onClick)||r.call(e,t),e.disabled||t.defaultPrevented||(t.currentTarget.focus(),n.open||n.onOpenChange(!0))},onPointerMove:(0,i.m)(e.onPointerMove,nx(t=>{u.onItemEnter(t),!t.defaultPrevented&&(e.disabled||n.open||s.current||(u.onPointerGraceIntentChange(null),s.current=window.setTimeout(()=>{n.onOpenChange(!0),p()},100)))})),onPointerLeave:(0,i.m)(e.onPointerLeave,nx(e=>{var t,r;p();let o=null==(t=n.content)?void 0:t.getBoundingClientRect();if(o){let t=null==(r=n.content)?void 0:r.dataset.side,i="right"===t,a=o[i?"left":"right"],l=o[i?"right":"left"];u.onPointerGraceIntentChange({area:[{x:e.clientX+(i?-5:5),y:e.clientY},{x:a,y:o.top},{x:l,y:o.top},{x:l,y:o.bottom},{x:a,y:o.bottom}],side:t}),window.clearTimeout(c.current),c.current=window.setTimeout(()=>u.onPointerGraceIntentChange(null),300)}else{if(u.onTriggerLeave(e),e.defaultPrevented)return;u.onPointerGraceIntentChange(null)}})),onKeyDown:(0,i.m)(e.onKeyDown,t=>{let o=""!==u.searchRef.current;if(!e.disabled&&(!o||" "!==t.key)&&tO[r.dir].includes(t.key)){var i;n.onOpenChange(!0),null==(i=n.content)||i.focus(),t.preventDefault()}})})})});nm.displayName=nh;var nv="MenuSubContent",ng=o.forwardRef((e,t)=>{let n=tq(t$,e.__scopeMenu),{forceMount:r=n.forceMount,...l}=e,u=tB(t$,e.__scopeMenu),s=tz(t$,e.__scopeMenu),c=np(nv,e.__scopeMenu),f=o.useRef(null),d=(0,a.s)(t,f);return(0,h.jsx)(tj.Provider,{scope:e.__scopeMenu,children:(0,h.jsx)(tl.C,{present:r||u.open,children:(0,h.jsx)(tj.Slot,{scope:e.__scopeMenu,children:(0,h.jsx)(t2,{id:c.contentId,"aria-labelledby":c.triggerId,...l,ref:d,align:"start",side:"rtl"===s.dir?"left":"right",disableOutsidePointerEvents:!1,disableOutsideScroll:!1,trapFocus:!1,onOpenAutoFocus:e=>{var t;s.isUsingKeyboardRef.current&&(null==(t=f.current)||t.focus()),e.preventDefault()},onCloseAutoFocus:e=>e.preventDefault(),onFocusOutside:(0,i.m)(e.onFocusOutside,e=>{e.target!==c.trigger&&u.onOpenChange(!1)}),onEscapeKeyDown:(0,i.m)(e.onEscapeKeyDown,e=>{s.onClose(),e.preventDefault()}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{let t=e.currentTarget.contains(e.target),n=tP[s.dir].includes(e.key);if(t&&n){var r;u.onOpenChange(!1),null==(r=c.trigger)||r.focus(),e.preventDefault()}})})})})})});function ny(e){return e?"open":"closed"}function nw(e){return"indeterminate"===e}function nb(e){return nw(e)?"indeterminate":e?"checked":"unchecked"}function nx(e){return t=>"mouse"===t.pointerType?e(t):void 0}ng.displayName=nv;var nE="DropdownMenu",[nR,nC]=(0,l.A)(nE,[t_]),nA=t_(),[nM,nS]=nR(nE),nk=e=>{let{__scopeDropdownMenu:t,children:n,dir:r,open:i,defaultOpen:a,onOpenChange:l,modal:s=!0}=e,c=nA(t),f=o.useRef(null),[d,p]=(0,u.i)({prop:i,defaultProp:null!=a&&a,onChange:l,caller:nE});return(0,h.jsx)(nM,{scope:t,triggerId:(0,C.B)(),triggerRef:f,contentId:(0,C.B)(),open:d,onOpenChange:p,onOpenToggle:o.useCallback(()=>p(e=>!e),[p]),modal:s,children:(0,h.jsx)(tK,{...c,open:d,onOpenChange:p,dir:r,modal:s,children:n})})};nk.displayName=nE;var nO="DropdownMenuTrigger",nP=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,disabled:r=!1,...o}=e,l=nS(nO,n),u=nA(n);return(0,h.jsx)(tG,{asChild:!0,...u,children:(0,h.jsx)(s.sG.button,{type:"button",id:l.triggerId,"aria-haspopup":"menu","aria-expanded":l.open,"aria-controls":l.open?l.contentId:void 0,"data-state":l.open?"open":"closed","data-disabled":r?"":void 0,disabled:r,...o,ref:(0,a.t)(t,l.triggerRef),onPointerDown:(0,i.m)(e.onPointerDown,e=>{!r&&0===e.button&&!1===e.ctrlKey&&(l.onOpenToggle(),l.open||e.preventDefault())}),onKeyDown:(0,i.m)(e.onKeyDown,e=>{!r&&(["Enter"," "].includes(e.key)&&l.onOpenToggle(),"ArrowDown"===e.key&&l.onOpenChange(!0),["Enter"," ","ArrowDown"].includes(e.key)&&e.preventDefault())})})})});nP.displayName=nO;var nN=e=>{let{__scopeDropdownMenu:t,...n}=e,r=nA(t);return(0,h.jsx)(tX,{...r,...n})};nN.displayName="DropdownMenuPortal";var nj="DropdownMenuContent",nT=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,a=nS(nj,n),l=nA(n),u=o.useRef(!1);return(0,h.jsx)(tJ,{id:a.contentId,"aria-labelledby":a.triggerId,...l,...r,ref:t,onCloseAutoFocus:(0,i.m)(e.onCloseAutoFocus,e=>{var t;u.current||null==(t=a.triggerRef.current)||t.focus(),u.current=!1,e.preventDefault()}),onInteractOutside:(0,i.m)(e.onInteractOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey,r=2===t.button||n;(!a.modal||r)&&(u.current=!0)}),style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})});nT.displayName=nj,o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nA(n);return(0,h.jsx)(t5,{...o,...r,ref:t})}).displayName="DropdownMenuGroup";var nD=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nA(n);return(0,h.jsx)(t4,{...o,...r,ref:t})});nD.displayName="DropdownMenuLabel";var nL=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nA(n);return(0,h.jsx)(t6,{...o,...r,ref:t})});nL.displayName="DropdownMenuItem",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nA(n);return(0,h.jsx)(t8,{...o,...r,ref:t})}).displayName="DropdownMenuCheckboxItem",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nA(n);return(0,h.jsx)(nr,{...o,...r,ref:t})}).displayName="DropdownMenuRadioGroup",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nA(n);return(0,h.jsx)(ni,{...o,...r,ref:t})}).displayName="DropdownMenuRadioItem",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nA(n);return(0,h.jsx)(ns,{...o,...r,ref:t})}).displayName="DropdownMenuItemIndicator";var n_=o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nA(n);return(0,h.jsx)(nc,{...o,...r,ref:t})});n_.displayName="DropdownMenuSeparator",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nA(n);return(0,h.jsx)(nf,{...o,...r,ref:t})}).displayName="DropdownMenuArrow",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nA(n);return(0,h.jsx)(nm,{...o,...r,ref:t})}).displayName="DropdownMenuSubTrigger",o.forwardRef((e,t)=>{let{__scopeDropdownMenu:n,...r}=e,o=nA(n);return(0,h.jsx)(ng,{...o,...r,ref:t,style:{...e.style,"--radix-dropdown-menu-content-transform-origin":"var(--radix-popper-transform-origin)","--radix-dropdown-menu-content-available-width":"var(--radix-popper-available-width)","--radix-dropdown-menu-content-available-height":"var(--radix-popper-available-height)","--radix-dropdown-menu-trigger-width":"var(--radix-popper-anchor-width)","--radix-dropdown-menu-trigger-height":"var(--radix-popper-anchor-height)"}})}).displayName="DropdownMenuSubContent";var nI=nk,nF=nP,nW=nN,nB=nT,nU=nD,nz=nL,nK=n_},2293:(e,t,n)=>{"use strict";n.d(t,{Oh:()=>i});var r=n(2115),o=0;function i(){r.useEffect(()=>{var e,t;let n=document.querySelectorAll("[data-radix-focus-guard]");return document.body.insertAdjacentElement("afterbegin",null!=(e=n[0])?e:a()),document.body.insertAdjacentElement("beforeend",null!=(t=n[1])?t:a()),o++,()=>{1===o&&document.querySelectorAll("[data-radix-focus-guard]").forEach(e=>e.remove()),o--}},[])}function a(){let e=document.createElement("span");return e.setAttribute("data-radix-focus-guard",""),e.tabIndex=0,e.style.outline="none",e.style.opacity="0",e.style.position="fixed",e.style.pointerEvents="none",e}},2436:(e,t,n)=>{"use strict";var r=n(2115),o="function"==typeof Object.is?Object.is:function(e,t){return e===t&&(0!==e||1/e==1/t)||e!=e&&t!=t},i=r.useState,a=r.useEffect,l=r.useLayoutEffect,u=r.useDebugValue;function s(e){var t=e.getSnapshot;e=e.value;try{var n=t();return!o(e,n)}catch(e){return!0}}var c="undefined"==typeof window||void 0===window.document||void 0===window.document.createElement?function(e,t){return t()}:function(e,t){var n=t(),r=i({inst:{value:n,getSnapshot:t}}),o=r[0].inst,c=r[1];return l(function(){o.value=n,o.getSnapshot=t,s(o)&&c({inst:o})},[e,n,t]),a(function(){return s(o)&&c({inst:o}),e(function(){s(o)&&c({inst:o})})},[e]),u(n),n};t.useSyncExternalStore=void 0!==r.useSyncExternalStore?r.useSyncExternalStore:c},2664:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"isLocalURL",{enumerable:!0,get:function(){return i}});let r=n(9991),o=n(7102);function i(e){if(!(0,r.isAbsoluteUrl)(e))return!0;try{let t=(0,r.getLocationOrigin)(),n=new URL(e,t);return n.origin===t&&(0,o.hasBasePath)(n.pathname)}catch(e){return!1}}},2712:(e,t,n)=>{"use strict";n.d(t,{N:()=>o});var r=n(2115),o=globalThis?.document?r.useLayoutEffect:()=>{}},2713:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("chart-column",[["path",{d:"M3 3v16a2 2 0 0 0 2 2h16",key:"c24i48"}],["path",{d:"M18 17V9",key:"2bz60n"}],["path",{d:"M13 17V5",key:"1frdt8"}],["path",{d:"M8 17v-3",key:"17ska0"}]])},2757:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{formatUrl:function(){return i},formatWithValidation:function(){return l},urlObjectKeys:function(){return a}});let r=n(6966)._(n(8859)),o=/https?|ftp|gopher|file/;function i(e){let{auth:t,hostname:n}=e,i=e.protocol||"",a=e.pathname||"",l=e.hash||"",u=e.query||"",s=!1;t=t?encodeURIComponent(t).replace(/%3A/i,":")+"@":"",e.host?s=t+e.host:n&&(s=t+(~n.indexOf(":")?"["+n+"]":n),e.port&&(s+=":"+e.port)),u&&"object"==typeof u&&(u=String(r.urlQueryToSearchParams(u)));let c=e.search||u&&"?"+u||"";return i&&!i.endsWith(":")&&(i+=":"),e.slashes||(!i||o.test(i))&&!1!==s?(s="//"+(s||""),a&&"/"!==a[0]&&(a="/"+a)):s||(s=""),l&&"#"!==l[0]&&(l="#"+l),c&&"?"!==c[0]&&(c="?"+c),""+i+s+(a=a.replace(/[?#]/g,encodeURIComponent))+(c=c.replace("#","%23"))+l}let a=["auth","hash","host","hostname","href","path","pathname","port","protocol","query","search","slashes"];function l(e){return i(e)}},3180:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"errorOnce",{enumerable:!0,get:function(){return n}});let n=e=>{}},3655:(e,t,n)=>{"use strict";n.d(t,{hO:()=>u,sG:()=>l});var r=n(2115),o=n(7650),i=n(9708),a=n(5155),l=["a","button","div","form","h2","h3","img","input","label","li","nav","ol","p","select","span","svg","ul"].reduce((e,t)=>{let n=(0,i.TL)(`Primitive.${t}`),o=r.forwardRef((e,r)=>{let{asChild:o,...i}=e;return"undefined"!=typeof window&&(window[Symbol.for("radix-ui")]=!0),(0,a.jsx)(o?n:t,{...i,ref:r})});return o.displayName=`Primitive.${t}`,{...e,[t]:o}},{});function u(e,t){e&&o.flushSync(()=>e.dispatchEvent(t))}},3795:(e,t,n)=>{"use strict";n.d(t,{A:()=>V});var r,o,i=function(){return(i=Object.assign||function(e){for(var t,n=1,r=arguments.length;n<r;n++)for(var o in t=arguments[n])Object.prototype.hasOwnProperty.call(t,o)&&(e[o]=t[o]);return e}).apply(this,arguments)};function a(e,t){var n={};for(var r in e)Object.prototype.hasOwnProperty.call(e,r)&&0>t.indexOf(r)&&(n[r]=e[r]);if(null!=e&&"function"==typeof Object.getOwnPropertySymbols)for(var o=0,r=Object.getOwnPropertySymbols(e);o<r.length;o++)0>t.indexOf(r[o])&&Object.prototype.propertyIsEnumerable.call(e,r[o])&&(n[r[o]]=e[r[o]]);return n}Object.create;Object.create;var l=("function"==typeof SuppressedError&&SuppressedError,n(2115)),u="right-scroll-bar-position",s="width-before-scroll-bar";function c(e,t){return"function"==typeof e?e(t):e&&(e.current=t),e}var f="undefined"!=typeof window?l.useLayoutEffect:l.useEffect,d=new WeakMap;function p(e){return e}var h=function(e){void 0===e&&(e={});var t,n,r,o=(void 0===t&&(t=p),n=[],r=!1,{read:function(){if(r)throw Error("Sidecar: could not `read` from an `assigned` medium. `read` could be used only with `useMedium`.");return n.length?n[n.length-1]:null},useMedium:function(e){var o=t(e,r);return n.push(o),function(){n=n.filter(function(e){return e!==o})}},assignSyncMedium:function(e){for(r=!0;n.length;){var t=n;n=[],t.forEach(e)}n={push:function(t){return e(t)},filter:function(){return n}}},assignMedium:function(e){r=!0;var t=[];if(n.length){var o=n;n=[],o.forEach(e),t=n}var i=function(){var n=t;t=[],n.forEach(e)},a=function(){return Promise.resolve().then(i)};a(),n={push:function(e){t.push(e),a()},filter:function(e){return t=t.filter(e),n}}}});return o.options=i({async:!0,ssr:!1},e),o}(),m=function(){},v=l.forwardRef(function(e,t){var n,r,o,u,s=l.useRef(null),p=l.useState({onScrollCapture:m,onWheelCapture:m,onTouchMoveCapture:m}),v=p[0],g=p[1],y=e.forwardProps,w=e.children,b=e.className,x=e.removeScrollBar,E=e.enabled,R=e.shards,C=e.sideCar,A=e.noRelative,M=e.noIsolation,S=e.inert,k=e.allowPinchZoom,O=e.as,P=e.gapMode,N=a(e,["forwardProps","children","className","removeScrollBar","enabled","shards","sideCar","noRelative","noIsolation","inert","allowPinchZoom","as","gapMode"]),j=(n=[s,t],r=function(e){return n.forEach(function(t){return c(t,e)})},(o=(0,l.useState)(function(){return{value:null,callback:r,facade:{get current(){return o.value},set current(value){var e=o.value;e!==value&&(o.value=value,o.callback(value,e))}}}})[0]).callback=r,u=o.facade,f(function(){var e=d.get(u);if(e){var t=new Set(e),r=new Set(n),o=u.current;t.forEach(function(e){r.has(e)||c(e,null)}),r.forEach(function(e){t.has(e)||c(e,o)})}d.set(u,n)},[n]),u),T=i(i({},N),v);return l.createElement(l.Fragment,null,E&&l.createElement(C,{sideCar:h,removeScrollBar:x,shards:R,noRelative:A,noIsolation:M,inert:S,setCallbacks:g,allowPinchZoom:!!k,lockRef:s,gapMode:P}),y?l.cloneElement(l.Children.only(w),i(i({},T),{ref:j})):l.createElement(void 0===O?"div":O,i({},T,{className:b,ref:j}),w))});v.defaultProps={enabled:!0,removeScrollBar:!0,inert:!1},v.classNames={fullWidth:s,zeroRight:u};var g=function(e){var t=e.sideCar,n=a(e,["sideCar"]);if(!t)throw Error("Sidecar: please provide `sideCar` property to import the right car");var r=t.read();if(!r)throw Error("Sidecar medium not found");return l.createElement(r,i({},n))};g.isSideCarExport=!0;var y=function(){var e=0,t=null;return{add:function(r){if(0==e&&(t=function(){if(!document)return null;var e=document.createElement("style");e.type="text/css";var t=o||n.nc;return t&&e.setAttribute("nonce",t),e}())){var i,a;(i=t).styleSheet?i.styleSheet.cssText=r:i.appendChild(document.createTextNode(r)),a=t,(document.head||document.getElementsByTagName("head")[0]).appendChild(a)}e++},remove:function(){--e||!t||(t.parentNode&&t.parentNode.removeChild(t),t=null)}}},w=function(){var e=y();return function(t,n){l.useEffect(function(){return e.add(t),function(){e.remove()}},[t&&n])}},b=function(){var e=w();return function(t){return e(t.styles,t.dynamic),null}},x={left:0,top:0,right:0,gap:0},E=function(e){return parseInt(e||"",10)||0},R=function(e){var t=window.getComputedStyle(document.body),n=t["padding"===e?"paddingLeft":"marginLeft"],r=t["padding"===e?"paddingTop":"marginTop"],o=t["padding"===e?"paddingRight":"marginRight"];return[E(n),E(r),E(o)]},C=function(e){if(void 0===e&&(e="margin"),"undefined"==typeof window)return x;var t=R(e),n=document.documentElement.clientWidth,r=window.innerWidth;return{left:t[0],top:t[1],right:t[2],gap:Math.max(0,r-n+t[2]-t[0])}},A=b(),M="data-scroll-locked",S=function(e,t,n,r){var o=e.left,i=e.top,a=e.right,l=e.gap;return void 0===n&&(n="margin"),"\n  .".concat("with-scroll-bars-hidden"," {\n   overflow: hidden ").concat(r,";\n   padding-right: ").concat(l,"px ").concat(r,";\n  }\n  body[").concat(M,"] {\n    overflow: hidden ").concat(r,";\n    overscroll-behavior: contain;\n    ").concat([t&&"position: relative ".concat(r,";"),"margin"===n&&"\n    padding-left: ".concat(o,"px;\n    padding-top: ").concat(i,"px;\n    padding-right: ").concat(a,"px;\n    margin-left:0;\n    margin-top:0;\n    margin-right: ").concat(l,"px ").concat(r,";\n    "),"padding"===n&&"padding-right: ".concat(l,"px ").concat(r,";")].filter(Boolean).join(""),"\n  }\n  \n  .").concat(u," {\n    right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(s," {\n    margin-right: ").concat(l,"px ").concat(r,";\n  }\n  \n  .").concat(u," .").concat(u," {\n    right: 0 ").concat(r,";\n  }\n  \n  .").concat(s," .").concat(s," {\n    margin-right: 0 ").concat(r,";\n  }\n  \n  body[").concat(M,"] {\n    ").concat("--removed-body-scroll-bar-size",": ").concat(l,"px;\n  }\n")},k=function(){var e=parseInt(document.body.getAttribute(M)||"0",10);return isFinite(e)?e:0},O=function(){l.useEffect(function(){return document.body.setAttribute(M,(k()+1).toString()),function(){var e=k()-1;e<=0?document.body.removeAttribute(M):document.body.setAttribute(M,e.toString())}},[])},P=function(e){var t=e.noRelative,n=e.noImportant,r=e.gapMode,o=void 0===r?"margin":r;O();var i=l.useMemo(function(){return C(o)},[o]);return l.createElement(A,{styles:S(i,!t,o,n?"":"!important")})},N=!1;if("undefined"!=typeof window)try{var j=Object.defineProperty({},"passive",{get:function(){return N=!0,!0}});window.addEventListener("test",j,j),window.removeEventListener("test",j,j)}catch(e){N=!1}var T=!!N&&{passive:!1},D=function(e,t){if(!(e instanceof Element))return!1;var n=window.getComputedStyle(e);return"hidden"!==n[t]&&(n.overflowY!==n.overflowX||"TEXTAREA"===e.tagName||"visible"!==n[t])},L=function(e,t){var n=t.ownerDocument,r=t;do{if("undefined"!=typeof ShadowRoot&&r instanceof ShadowRoot&&(r=r.host),_(e,r)){var o=I(e,r);if(o[1]>o[2])return!0}r=r.parentNode}while(r&&r!==n.body);return!1},_=function(e,t){return"v"===e?D(t,"overflowY"):D(t,"overflowX")},I=function(e,t){return"v"===e?[t.scrollTop,t.scrollHeight,t.clientHeight]:[t.scrollLeft,t.scrollWidth,t.clientWidth]},F=function(e,t,n,r,o){var i,a=(i=window.getComputedStyle(t).direction,"h"===e&&"rtl"===i?-1:1),l=a*r,u=n.target,s=t.contains(u),c=!1,f=l>0,d=0,p=0;do{if(!u)break;var h=I(e,u),m=h[0],v=h[1]-h[2]-a*m;(m||v)&&_(e,u)&&(d+=v,p+=m);var g=u.parentNode;u=g&&g.nodeType===Node.DOCUMENT_FRAGMENT_NODE?g.host:g}while(!s&&u!==document.body||s&&(t.contains(u)||t===u));return f&&(o&&1>Math.abs(d)||!o&&l>d)?c=!0:!f&&(o&&1>Math.abs(p)||!o&&-l>p)&&(c=!0),c},W=function(e){return"changedTouches"in e?[e.changedTouches[0].clientX,e.changedTouches[0].clientY]:[0,0]},B=function(e){return[e.deltaX,e.deltaY]},U=function(e){return e&&"current"in e?e.current:e},z=0,K=[];let G=(r=function(e){var t=l.useRef([]),n=l.useRef([0,0]),r=l.useRef(),o=l.useState(z++)[0],i=l.useState(b)[0],a=l.useRef(e);l.useEffect(function(){a.current=e},[e]),l.useEffect(function(){if(e.inert){document.body.classList.add("block-interactivity-".concat(o));var t=(function(e,t,n){if(n||2==arguments.length)for(var r,o=0,i=t.length;o<i;o++)!r&&o in t||(r||(r=Array.prototype.slice.call(t,0,o)),r[o]=t[o]);return e.concat(r||Array.prototype.slice.call(t))})([e.lockRef.current],(e.shards||[]).map(U),!0).filter(Boolean);return t.forEach(function(e){return e.classList.add("allow-interactivity-".concat(o))}),function(){document.body.classList.remove("block-interactivity-".concat(o)),t.forEach(function(e){return e.classList.remove("allow-interactivity-".concat(o))})}}},[e.inert,e.lockRef.current,e.shards]);var u=l.useCallback(function(e,t){if("touches"in e&&2===e.touches.length||"wheel"===e.type&&e.ctrlKey)return!a.current.allowPinchZoom;var o,i=W(e),l=n.current,u="deltaX"in e?e.deltaX:l[0]-i[0],s="deltaY"in e?e.deltaY:l[1]-i[1],c=e.target,f=Math.abs(u)>Math.abs(s)?"h":"v";if("touches"in e&&"h"===f&&"range"===c.type)return!1;var d=L(f,c);if(!d)return!0;if(d?o=f:(o="v"===f?"h":"v",d=L(f,c)),!d)return!1;if(!r.current&&"changedTouches"in e&&(u||s)&&(r.current=o),!o)return!0;var p=r.current||o;return F(p,t,e,"h"===p?u:s,!0)},[]),s=l.useCallback(function(e){if(K.length&&K[K.length-1]===i){var n="deltaY"in e?B(e):W(e),r=t.current.filter(function(t){var r;return t.name===e.type&&(t.target===e.target||e.target===t.shadowParent)&&(r=t.delta,r[0]===n[0]&&r[1]===n[1])})[0];if(r&&r.should){e.cancelable&&e.preventDefault();return}if(!r){var o=(a.current.shards||[]).map(U).filter(Boolean).filter(function(t){return t.contains(e.target)});(o.length>0?u(e,o[0]):!a.current.noIsolation)&&e.cancelable&&e.preventDefault()}}},[]),c=l.useCallback(function(e,n,r,o){var i={name:e,delta:n,target:r,should:o,shadowParent:function(e){for(var t=null;null!==e;)e instanceof ShadowRoot&&(t=e.host,e=e.host),e=e.parentNode;return t}(r)};t.current.push(i),setTimeout(function(){t.current=t.current.filter(function(e){return e!==i})},1)},[]),f=l.useCallback(function(e){n.current=W(e),r.current=void 0},[]),d=l.useCallback(function(t){c(t.type,B(t),t.target,u(t,e.lockRef.current))},[]),p=l.useCallback(function(t){c(t.type,W(t),t.target,u(t,e.lockRef.current))},[]);l.useEffect(function(){return K.push(i),e.setCallbacks({onScrollCapture:d,onWheelCapture:d,onTouchMoveCapture:p}),document.addEventListener("wheel",s,T),document.addEventListener("touchmove",s,T),document.addEventListener("touchstart",f,T),function(){K=K.filter(function(e){return e!==i}),document.removeEventListener("wheel",s,T),document.removeEventListener("touchmove",s,T),document.removeEventListener("touchstart",f,T)}},[]);var h=e.removeScrollBar,m=e.inert;return l.createElement(l.Fragment,null,m?l.createElement(i,{styles:"\n  .block-interactivity-".concat(o," {pointer-events: none;}\n  .allow-interactivity-").concat(o," {pointer-events: all;}\n")}):null,h?l.createElement(P,{noRelative:e.noRelative,gapMode:e.gapMode}):null)},h.useMedium(r),g);var H=l.forwardRef(function(e,t){return l.createElement(v,i({},e,{ref:t,sideCar:G}))});H.classNames=v.classNames;let V=H},3861:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("bell",[["path",{d:"M10.268 21a2 2 0 0 0 3.464 0",key:"vwvbt9"}],["path",{d:"M3.262 15.326A1 1 0 0 0 4 17h16a1 1 0 0 0 .74-1.673C19.41 13.956 18 12.499 18 8A6 6 0 0 0 6 8c0 4.499-1.411 5.956-2.738 7.326",key:"11g9vi"}]])},4011:(e,t,n)=>{"use strict";n.d(t,{H4:()=>C,_V:()=>R,bL:()=>E});var r=n(2115),o=n(6081),i=n(9033),a=n(2712),l=n(3655),u=n(1414);function s(){return()=>{}}var c=n(5155),f="Avatar",[d,p]=(0,o.A)(f),[h,m]=d(f),v=r.forwardRef((e,t)=>{let{__scopeAvatar:n,...o}=e,[i,a]=r.useState("idle");return(0,c.jsx)(h,{scope:n,imageLoadingStatus:i,onImageLoadingStatusChange:a,children:(0,c.jsx)(l.sG.span,{...o,ref:t})})});v.displayName=f;var g="AvatarImage",y=r.forwardRef((e,t)=>{let{__scopeAvatar:n,src:o,onLoadingStatusChange:f=()=>{},...d}=e,p=m(g,n),h=function(e,t){let{referrerPolicy:n,crossOrigin:o}=t,i=(0,u.useSyncExternalStore)(s,()=>!0,()=>!1),l=r.useRef(null),c=i?(l.current||(l.current=new window.Image),l.current):null,[f,d]=r.useState(()=>x(c,e));return(0,a.N)(()=>{d(x(c,e))},[c,e]),(0,a.N)(()=>{let e=e=>()=>{d(e)};if(!c)return;let t=e("loaded"),r=e("error");return c.addEventListener("load",t),c.addEventListener("error",r),n&&(c.referrerPolicy=n),"string"==typeof o&&(c.crossOrigin=o),()=>{c.removeEventListener("load",t),c.removeEventListener("error",r)}},[c,o,n]),f}(o,d),v=(0,i.c)(e=>{f(e),p.onImageLoadingStatusChange(e)});return(0,a.N)(()=>{"idle"!==h&&v(h)},[h,v]),"loaded"===h?(0,c.jsx)(l.sG.img,{...d,ref:t,src:o}):null});y.displayName=g;var w="AvatarFallback",b=r.forwardRef((e,t)=>{let{__scopeAvatar:n,delayMs:o,...i}=e,a=m(w,n),[u,s]=r.useState(void 0===o);return r.useEffect(()=>{if(void 0!==o){let e=window.setTimeout(()=>s(!0),o);return()=>window.clearTimeout(e)}},[o]),u&&"loaded"!==a.imageLoadingStatus?(0,c.jsx)(l.sG.span,{...i,ref:t}):null});function x(e,t){return e?t?(e.src!==t&&(e.src=t),e.complete&&e.naturalWidth>0?"loaded":"loading"):"error":"idle"}b.displayName=w;var E=v,R=y,C=b},4147:e=>{e.exports={style:{fontFamily:"'Geist', 'Geist Fallback'",fontStyle:"normal"},className:"__className_5cfdac",variable:"__variable_5cfdac"}},4378:(e,t,n)=>{"use strict";n.d(t,{Z:()=>u});var r=n(2115),o=n(7650),i=n(3655),a=n(2712),l=n(5155),u=r.forwardRef((e,t)=>{var n,u;let{container:s,...c}=e,[f,d]=r.useState(!1);(0,a.N)(()=>d(!0),[]);let p=s||f&&(null==(u=globalThis)||null==(n=u.document)?void 0:n.body);return p?o.createPortal((0,l.jsx)(i.sG.div,{...c,ref:t}),p):null});u.displayName="Portal"},4416:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("x",[["path",{d:"M18 6 6 18",key:"1bl5f8"}],["path",{d:"m6 6 12 12",key:"d8bk6v"}]])},4783:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("menu",[["path",{d:"M4 12h16",key:"1lakjw"}],["path",{d:"M4 18h16",key:"19g7jn"}],["path",{d:"M4 6h16",key:"1o0s65"}]])},5185:(e,t,n)=>{"use strict";function r(e,t,{checkForDefaultPrevented:n=!0}={}){return function(r){if(e?.(r),!1===n||!r.defaultPrevented)return t?.(r)}}n.d(t,{m:()=>r})},5452:(e,t,n)=>{"use strict";n.d(t,{UC:()=>ee,ZL:()=>J,bL:()=>Y,bm:()=>et,hJ:()=>Q,l9:()=>Z});var r=n(2115),o=n(5185),i=n(6101),a=n(6081),l=n(1285),u=n(5845),s=n(9178),c=n(7900),f=n(4378),d=n(8905),p=n(3655),h=n(2293),m=n(3795),v=n(8168),g=n(9708),y=n(5155),w="Dialog",[b,x]=(0,a.A)(w),[E,R]=b(w),C=e=>{let{__scopeDialog:t,children:n,open:o,defaultOpen:i,onOpenChange:a,modal:s=!0}=e,c=r.useRef(null),f=r.useRef(null),[d,p]=(0,u.i)({prop:o,defaultProp:null!=i&&i,onChange:a,caller:w});return(0,y.jsx)(E,{scope:t,triggerRef:c,contentRef:f,contentId:(0,l.B)(),titleId:(0,l.B)(),descriptionId:(0,l.B)(),open:d,onOpenChange:p,onOpenToggle:r.useCallback(()=>p(e=>!e),[p]),modal:s,children:n})};C.displayName=w;var A="DialogTrigger",M=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,a=R(A,n),l=(0,i.s)(t,a.triggerRef);return(0,y.jsx)(p.sG.button,{type:"button","aria-haspopup":"dialog","aria-expanded":a.open,"aria-controls":a.contentId,"data-state":G(a.open),...r,ref:l,onClick:(0,o.m)(e.onClick,a.onOpenToggle)})});M.displayName=A;var S="DialogPortal",[k,O]=b(S,{forceMount:void 0}),P=e=>{let{__scopeDialog:t,forceMount:n,children:o,container:i}=e,a=R(S,t);return(0,y.jsx)(k,{scope:t,forceMount:n,children:r.Children.map(o,e=>(0,y.jsx)(d.C,{present:n||a.open,children:(0,y.jsx)(f.Z,{asChild:!0,container:i,children:e})}))})};P.displayName=S;var N="DialogOverlay",j=r.forwardRef((e,t)=>{let n=O(N,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=R(N,e.__scopeDialog);return i.modal?(0,y.jsx)(d.C,{present:r||i.open,children:(0,y.jsx)(D,{...o,ref:t})}):null});j.displayName=N;var T=(0,g.TL)("DialogOverlay.RemoveScroll"),D=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(N,n);return(0,y.jsx)(m.A,{as:T,allowPinchZoom:!0,shards:[o.contentRef],children:(0,y.jsx)(p.sG.div,{"data-state":G(o.open),...r,ref:t,style:{pointerEvents:"auto",...r.style}})})}),L="DialogContent",_=r.forwardRef((e,t)=>{let n=O(L,e.__scopeDialog),{forceMount:r=n.forceMount,...o}=e,i=R(L,e.__scopeDialog);return(0,y.jsx)(d.C,{present:r||i.open,children:i.modal?(0,y.jsx)(I,{...o,ref:t}):(0,y.jsx)(F,{...o,ref:t})})});_.displayName=L;var I=r.forwardRef((e,t)=>{let n=R(L,e.__scopeDialog),a=r.useRef(null),l=(0,i.s)(t,n.contentRef,a);return r.useEffect(()=>{let e=a.current;if(e)return(0,v.Eq)(e)},[]),(0,y.jsx)(W,{...e,ref:l,trapFocus:n.open,disableOutsidePointerEvents:!0,onCloseAutoFocus:(0,o.m)(e.onCloseAutoFocus,e=>{var t;e.preventDefault(),null==(t=n.triggerRef.current)||t.focus()}),onPointerDownOutside:(0,o.m)(e.onPointerDownOutside,e=>{let t=e.detail.originalEvent,n=0===t.button&&!0===t.ctrlKey;(2===t.button||n)&&e.preventDefault()}),onFocusOutside:(0,o.m)(e.onFocusOutside,e=>e.preventDefault())})}),F=r.forwardRef((e,t)=>{let n=R(L,e.__scopeDialog),o=r.useRef(!1),i=r.useRef(!1);return(0,y.jsx)(W,{...e,ref:t,trapFocus:!1,disableOutsidePointerEvents:!1,onCloseAutoFocus:t=>{var r,a;null==(r=e.onCloseAutoFocus)||r.call(e,t),t.defaultPrevented||(o.current||null==(a=n.triggerRef.current)||a.focus(),t.preventDefault()),o.current=!1,i.current=!1},onInteractOutside:t=>{var r,a;null==(r=e.onInteractOutside)||r.call(e,t),t.defaultPrevented||(o.current=!0,"pointerdown"===t.detail.originalEvent.type&&(i.current=!0));let l=t.target;(null==(a=n.triggerRef.current)?void 0:a.contains(l))&&t.preventDefault(),"focusin"===t.detail.originalEvent.type&&i.current&&t.preventDefault()}})}),W=r.forwardRef((e,t)=>{let{__scopeDialog:n,trapFocus:o,onOpenAutoFocus:a,onCloseAutoFocus:l,...u}=e,f=R(L,n),d=r.useRef(null),p=(0,i.s)(t,d);return(0,h.Oh)(),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(c.n,{asChild:!0,loop:!0,trapped:o,onMountAutoFocus:a,onUnmountAutoFocus:l,children:(0,y.jsx)(s.qW,{role:"dialog",id:f.contentId,"aria-describedby":f.descriptionId,"aria-labelledby":f.titleId,"data-state":G(f.open),...u,ref:p,onDismiss:()=>f.onOpenChange(!1)})}),(0,y.jsxs)(y.Fragment,{children:[(0,y.jsx)(X,{titleId:f.titleId}),(0,y.jsx)($,{contentRef:d,descriptionId:f.descriptionId})]})]})}),B="DialogTitle";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(B,n);return(0,y.jsx)(p.sG.h2,{id:o.titleId,...r,ref:t})}).displayName=B;var U="DialogDescription";r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,o=R(U,n);return(0,y.jsx)(p.sG.p,{id:o.descriptionId,...r,ref:t})}).displayName=U;var z="DialogClose",K=r.forwardRef((e,t)=>{let{__scopeDialog:n,...r}=e,i=R(z,n);return(0,y.jsx)(p.sG.button,{type:"button",...r,ref:t,onClick:(0,o.m)(e.onClick,()=>i.onOpenChange(!1))})});function G(e){return e?"open":"closed"}K.displayName=z;var H="DialogTitleWarning",[V,q]=(0,a.q)(H,{contentName:L,titleName:B,docsSlug:"dialog"}),X=e=>{let{titleId:t}=e,n=q(H),o="`".concat(n.contentName,"` requires a `").concat(n.titleName,"` for the component to be accessible for screen reader users.\n\nIf you want to hide the `").concat(n.titleName,"`, you can wrap it with our VisuallyHidden component.\n\nFor more information, see https://radix-ui.com/primitives/docs/components/").concat(n.docsSlug);return r.useEffect(()=>{t&&(document.getElementById(t)||console.error(o))},[o,t]),null},$=e=>{let{contentRef:t,descriptionId:n}=e,o=q("DialogDescriptionWarning"),i="Warning: Missing `Description` or `aria-describedby={undefined}` for {".concat(o.contentName,"}.");return r.useEffect(()=>{var e;let r=null==(e=t.current)?void 0:e.getAttribute("aria-describedby");n&&r&&(document.getElementById(n)||console.warn(i))},[i,t,n]),null},Y=C,Z=M,J=P,Q=j,ee=_,et=K},5670:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("user-check",[["path",{d:"m16 11 2 2 4-4",key:"9rsbq5"}],["path",{d:"M16 21v-2a4 4 0 0 0-4-4H6a4 4 0 0 0-4 4v2",key:"1yyitq"}],["circle",{cx:"9",cy:"7",r:"4",key:"nufk8"}]])},5695:(e,t,n)=>{"use strict";var r=n(8999);n.o(r,"usePathname")&&n.d(t,{usePathname:function(){return r.usePathname}})},5845:(e,t,n)=>{"use strict";n.d(t,{i:()=>l});var r,o=n(2115),i=n(2712),a=(r||(r=n.t(o,2)))[" useInsertionEffect ".trim().toString()]||i.N;function l({prop:e,defaultProp:t,onChange:n=()=>{},caller:r}){let[i,l,u]=function({defaultProp:e,onChange:t}){let[n,r]=o.useState(e),i=o.useRef(n),l=o.useRef(t);return a(()=>{l.current=t},[t]),o.useEffect(()=>{i.current!==n&&(l.current?.(n),i.current=n)},[n,i]),[n,r,l]}({defaultProp:t,onChange:n}),s=void 0!==e,c=s?e:i;{let t=o.useRef(void 0!==e);o.useEffect(()=>{let e=t.current;if(e!==s){let t=s?"controlled":"uncontrolled";console.warn(`${r} is changing from ${e?"controlled":"uncontrolled"} to ${t}. Components should not switch from controlled to uncontrolled (or vice versa). Decide between using a controlled or uncontrolled value for the lifetime of the component.`)}t.current=s},[s,r])}return[c,o.useCallback(t=>{if(s){let n="function"==typeof t?t(e):t;n!==e&&u.current?.(n)}else l(t)},[s,e,l,u])]}Symbol("RADIX:SYNC_STATE")},5937:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("store",[["path",{d:"m2 7 4.41-4.41A2 2 0 0 1 7.83 2h8.34a2 2 0 0 1 1.42.59L22 7",key:"ztvudi"}],["path",{d:"M4 12v8a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2v-8",key:"1b2hhj"}],["path",{d:"M15 22v-4a2 2 0 0 0-2-2h-2a2 2 0 0 0-2 2v4",key:"2ebpfo"}],["path",{d:"M2 7h20",key:"1fcdvo"}],["path",{d:"M22 7v3a2 2 0 0 1-2 2a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 16 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 12 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 8 12a2.7 2.7 0 0 1-1.59-.63.7.7 0 0 0-.82 0A2.7 2.7 0 0 1 4 12a2 2 0 0 1-2-2V7",key:"6c3vgh"}]])},6081:(e,t,n)=>{"use strict";n.d(t,{A:()=>a,q:()=>i});var r=n(2115),o=n(5155);function i(e,t){let n=r.createContext(t),i=e=>{let{children:t,...i}=e,a=r.useMemo(()=>i,Object.values(i));return(0,o.jsx)(n.Provider,{value:a,children:t})};return i.displayName=e+"Provider",[i,function(o){let i=r.useContext(n);if(i)return i;if(void 0!==t)return t;throw Error(`\`${o}\` must be used within \`${e}\``)}]}function a(e,t=[]){let n=[],i=()=>{let t=n.map(e=>r.createContext(e));return function(n){let o=n?.[e]||t;return r.useMemo(()=>({[`__scope${e}`]:{...n,[e]:o}}),[n,o])}};return i.scopeName=e,[function(t,i){let a=r.createContext(i),l=n.length;n=[...n,i];let u=t=>{let{scope:n,children:i,...u}=t,s=n?.[e]?.[l]||a,c=r.useMemo(()=>u,Object.values(u));return(0,o.jsx)(s.Provider,{value:c,children:i})};return u.displayName=t+"Provider",[u,function(n,o){let u=o?.[e]?.[l]||a,s=r.useContext(u);if(s)return s;if(void 0!==i)return i;throw Error(`\`${n}\` must be used within \`${t}\``)}]},function(...e){let t=e[0];if(1===e.length)return t;let n=()=>{let n=e.map(e=>({useScope:e(),scopeName:e.scopeName}));return function(e){let o=n.reduce((t,{useScope:n,scopeName:r})=>{let o=n(e)[`__scope${r}`];return{...t,...o}},{});return r.useMemo(()=>({[`__scope${t.scopeName}`]:o}),[o])}};return n.scopeName=t.scopeName,n}(i,...t)]}},6654:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),Object.defineProperty(t,"useMergedRef",{enumerable:!0,get:function(){return o}});let r=n(2115);function o(e,t){let n=(0,r.useRef)(null),o=(0,r.useRef)(null);return(0,r.useCallback)(r=>{if(null===r){let e=n.current;e&&(n.current=null,e());let t=o.current;t&&(o.current=null,t())}else e&&(n.current=i(e,r)),t&&(o.current=i(t,r))},[e,t])}function i(e,t){if("function"!=typeof e)return e.current=t,()=>{e.current=null};{let n=e(t);return"function"==typeof n?n:()=>e(null)}}("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},6874:(e,t,n)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{default:function(){return v},useLinkStatus:function(){return y}});let r=n(6966),o=n(5155),i=r._(n(2115)),a=n(2757),l=n(5227),u=n(9818),s=n(6654),c=n(9991),f=n(5929);n(3230);let d=n(4930),p=n(2664),h=n(6634);function m(e){return"string"==typeof e?e:(0,a.formatUrl)(e)}function v(e){let t,n,r,[a,v]=(0,i.useOptimistic)(d.IDLE_LINK_STATUS),y=(0,i.useRef)(null),{href:w,as:b,children:x,prefetch:E=null,passHref:R,replace:C,shallow:A,scroll:M,onClick:S,onMouseEnter:k,onTouchStart:O,legacyBehavior:P=!1,onNavigate:N,ref:j,unstable_dynamicOnHover:T,...D}=e;t=x,P&&("string"==typeof t||"number"==typeof t)&&(t=(0,o.jsx)("a",{children:t}));let L=i.default.useContext(l.AppRouterContext),_=!1!==E,I=null===E||"auto"===E?u.PrefetchKind.AUTO:u.PrefetchKind.FULL,{href:F,as:W}=i.default.useMemo(()=>{let e=m(w);return{href:e,as:b?m(b):e}},[w,b]);P&&(n=i.default.Children.only(t));let B=P?n&&"object"==typeof n&&n.ref:j,U=i.default.useCallback(e=>(null!==L&&(y.current=(0,d.mountLinkInstance)(e,F,L,I,_,v)),()=>{y.current&&((0,d.unmountLinkForCurrentNavigation)(y.current),y.current=null),(0,d.unmountPrefetchableInstance)(e)}),[_,F,L,I,v]),z={ref:(0,s.useMergedRef)(U,B),onClick(e){P||"function"!=typeof S||S(e),P&&n.props&&"function"==typeof n.props.onClick&&n.props.onClick(e),L&&(e.defaultPrevented||function(e,t,n,r,o,a,l){let{nodeName:u}=e.currentTarget;if(!("A"===u.toUpperCase()&&function(e){let t=e.currentTarget.getAttribute("target");return t&&"_self"!==t||e.metaKey||e.ctrlKey||e.shiftKey||e.altKey||e.nativeEvent&&2===e.nativeEvent.which}(e)||e.currentTarget.hasAttribute("download"))){if(!(0,p.isLocalURL)(t)){o&&(e.preventDefault(),location.replace(t));return}if(e.preventDefault(),l){let e=!1;if(l({preventDefault:()=>{e=!0}}),e)return}i.default.startTransition(()=>{(0,h.dispatchNavigateAction)(n||t,o?"replace":"push",null==a||a,r.current)})}}(e,F,W,y,C,M,N))},onMouseEnter(e){P||"function"!=typeof k||k(e),P&&n.props&&"function"==typeof n.props.onMouseEnter&&n.props.onMouseEnter(e),L&&_&&(0,d.onNavigationIntent)(e.currentTarget,!0===T)},onTouchStart:function(e){P||"function"!=typeof O||O(e),P&&n.props&&"function"==typeof n.props.onTouchStart&&n.props.onTouchStart(e),L&&_&&(0,d.onNavigationIntent)(e.currentTarget,!0===T)}};return(0,c.isAbsoluteUrl)(W)?z.href=W:P&&!R&&("a"!==n.type||"href"in n.props)||(z.href=(0,f.addBasePath)(W)),r=P?i.default.cloneElement(n,z):(0,o.jsx)("a",{...D,...z,children:t}),(0,o.jsx)(g.Provider,{value:a,children:r})}n(3180);let g=(0,i.createContext)(d.IDLE_LINK_STATUS),y=()=>(0,i.useContext)(g);("function"==typeof t.default||"object"==typeof t.default&&null!==t.default)&&void 0===t.default.__esModule&&(Object.defineProperty(t.default,"__esModule",{value:!0}),Object.assign(t.default,t),e.exports=t.default)},7340:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("house",[["path",{d:"M15 21v-8a1 1 0 0 0-1-1h-4a1 1 0 0 0-1 1v8",key:"5wwlr5"}],["path",{d:"M3 10a2 2 0 0 1 .709-1.528l7-5.999a2 2 0 0 1 2.582 0l7 5.999A2 2 0 0 1 21 10v9a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z",key:"1d0kgt"}]])},7434:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("file-text",[["path",{d:"M15 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V7Z",key:"1rqfz7"}],["path",{d:"M14 2v4a2 2 0 0 0 2 2h4",key:"tnqrlb"}],["path",{d:"M10 9H8",key:"b1mrlr"}],["path",{d:"M16 13H8",key:"t4e002"}],["path",{d:"M16 17H8",key:"z1uh3a"}]])},7900:(e,t,n)=>{"use strict";n.d(t,{n:()=>f});var r=n(2115),o=n(6101),i=n(3655),a=n(9033),l=n(5155),u="focusScope.autoFocusOnMount",s="focusScope.autoFocusOnUnmount",c={bubbles:!1,cancelable:!0},f=r.forwardRef((e,t)=>{let{loop:n=!1,trapped:f=!1,onMountAutoFocus:v,onUnmountAutoFocus:g,...y}=e,[w,b]=r.useState(null),x=(0,a.c)(v),E=(0,a.c)(g),R=r.useRef(null),C=(0,o.s)(t,e=>b(e)),A=r.useRef({paused:!1,pause(){this.paused=!0},resume(){this.paused=!1}}).current;r.useEffect(()=>{if(f){let e=function(e){if(A.paused||!w)return;let t=e.target;w.contains(t)?R.current=t:h(R.current,{select:!0})},t=function(e){if(A.paused||!w)return;let t=e.relatedTarget;null!==t&&(w.contains(t)||h(R.current,{select:!0}))};document.addEventListener("focusin",e),document.addEventListener("focusout",t);let n=new MutationObserver(function(e){if(document.activeElement===document.body)for(let t of e)t.removedNodes.length>0&&h(w)});return w&&n.observe(w,{childList:!0,subtree:!0}),()=>{document.removeEventListener("focusin",e),document.removeEventListener("focusout",t),n.disconnect()}}},[f,w,A.paused]),r.useEffect(()=>{if(w){m.add(A);let e=document.activeElement;if(!w.contains(e)){let t=new CustomEvent(u,c);w.addEventListener(u,x),w.dispatchEvent(t),t.defaultPrevented||(function(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{},n=document.activeElement;for(let r of e)if(h(r,{select:t}),document.activeElement!==n)return}(d(w).filter(e=>"A"!==e.tagName),{select:!0}),document.activeElement===e&&h(w))}return()=>{w.removeEventListener(u,x),setTimeout(()=>{let t=new CustomEvent(s,c);w.addEventListener(s,E),w.dispatchEvent(t),t.defaultPrevented||h(null!=e?e:document.body,{select:!0}),w.removeEventListener(s,E),m.remove(A)},0)}}},[w,x,E,A]);let M=r.useCallback(e=>{if(!n&&!f||A.paused)return;let t="Tab"===e.key&&!e.altKey&&!e.ctrlKey&&!e.metaKey,r=document.activeElement;if(t&&r){let t=e.currentTarget,[o,i]=function(e){let t=d(e);return[p(t,e),p(t.reverse(),e)]}(t);o&&i?e.shiftKey||r!==i?e.shiftKey&&r===o&&(e.preventDefault(),n&&h(i,{select:!0})):(e.preventDefault(),n&&h(o,{select:!0})):r===t&&e.preventDefault()}},[n,f,A.paused]);return(0,l.jsx)(i.sG.div,{tabIndex:-1,...y,ref:C,onKeyDown:M})});function d(e){let t=[],n=document.createTreeWalker(e,NodeFilter.SHOW_ELEMENT,{acceptNode:e=>{let t="INPUT"===e.tagName&&"hidden"===e.type;return e.disabled||e.hidden||t?NodeFilter.FILTER_SKIP:e.tabIndex>=0?NodeFilter.FILTER_ACCEPT:NodeFilter.FILTER_SKIP}});for(;n.nextNode();)t.push(n.currentNode);return t}function p(e,t){for(let n of e)if(!function(e,t){let{upTo:n}=t;if("hidden"===getComputedStyle(e).visibility)return!0;for(;e&&(void 0===n||e!==n);){if("none"===getComputedStyle(e).display)return!0;e=e.parentElement}return!1}(n,{upTo:t}))return n}function h(e){let{select:t=!1}=arguments.length>1&&void 0!==arguments[1]?arguments[1]:{};if(e&&e.focus){var n;let r=document.activeElement;e.focus({preventScroll:!0}),e!==r&&(n=e)instanceof HTMLInputElement&&"select"in n&&t&&e.select()}}f.displayName="FocusScope";var m=function(){let e=[];return{add(t){let n=e[0];t!==n&&(null==n||n.pause()),(e=v(e,t)).unshift(t)},remove(t){var n;null==(n=(e=v(e,t))[0])||n.resume()}}}();function v(e,t){let n=[...e],r=n.indexOf(t);return -1!==r&&n.splice(r,1),n}},7924:(e,t,n)=>{"use strict";n.d(t,{A:()=>r});let r=(0,n(9946).A)("search",[["path",{d:"m21 21-4.34-4.34",key:"14j7rj"}],["circle",{cx:"11",cy:"11",r:"8",key:"4ej97u"}]])},8168:(e,t,n)=>{"use strict";n.d(t,{Eq:()=>s});var r=new WeakMap,o=new WeakMap,i={},a=0,l=function(e){return e&&(e.host||l(e.parentNode))},u=function(e,t,n,u){var s=(Array.isArray(e)?e:[e]).map(function(e){if(t.contains(e))return e;var n=l(e);return n&&t.contains(n)?n:(console.error("aria-hidden",e,"in not contained inside",t,". Doing nothing"),null)}).filter(function(e){return!!e});i[n]||(i[n]=new WeakMap);var c=i[n],f=[],d=new Set,p=new Set(s),h=function(e){!e||d.has(e)||(d.add(e),h(e.parentNode))};s.forEach(h);var m=function(e){!e||p.has(e)||Array.prototype.forEach.call(e.children,function(e){if(d.has(e))m(e);else try{var t=e.getAttribute(u),i=null!==t&&"false"!==t,a=(r.get(e)||0)+1,l=(c.get(e)||0)+1;r.set(e,a),c.set(e,l),f.push(e),1===a&&i&&o.set(e,!0),1===l&&e.setAttribute(n,"true"),i||e.setAttribute(u,"true")}catch(t){console.error("aria-hidden: cannot operate on ",e,t)}})};return m(t),d.clear(),a++,function(){f.forEach(function(e){var t=r.get(e)-1,i=c.get(e)-1;r.set(e,t),c.set(e,i),t||(o.has(e)||e.removeAttribute(u),o.delete(e)),i||e.removeAttribute(n)}),--a||(r=new WeakMap,r=new WeakMap,o=new WeakMap,i={})}},s=function(e,t,n){void 0===n&&(n="data-aria-hidden");var r=Array.from(Array.isArray(e)?e:[e]),o=t||("undefined"==typeof document?null:(Array.isArray(e)?e[0]:e).ownerDocument.body);return o?(r.push.apply(r,Array.from(o.querySelectorAll("[aria-live], script"))),u(r,o,n,"aria-hidden")):function(){return null}}},8489:e=>{e.exports={style:{fontFamily:"'Geist Mono', 'Geist Mono Fallback'",fontStyle:"normal"},className:"__className_9a8899",variable:"__variable_9a8899"}},8859:(e,t)=>{"use strict";function n(e){let t={};for(let[n,r]of e.entries()){let e=t[n];void 0===e?t[n]=r:Array.isArray(e)?e.push(r):t[n]=[e,r]}return t}function r(e){return"string"==typeof e?e:("number"!=typeof e||isNaN(e))&&"boolean"!=typeof e?"":String(e)}function o(e){let t=new URLSearchParams;for(let[n,o]of Object.entries(e))if(Array.isArray(o))for(let e of o)t.append(n,r(e));else t.set(n,r(o));return t}function i(e){for(var t=arguments.length,n=Array(t>1?t-1:0),r=1;r<t;r++)n[r-1]=arguments[r];for(let t of n){for(let n of t.keys())e.delete(n);for(let[n,r]of t.entries())e.append(n,r)}return e}Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{assign:function(){return i},searchParamsToUrlQuery:function(){return n},urlQueryToSearchParams:function(){return o}})},8905:(e,t,n)=>{"use strict";n.d(t,{C:()=>a});var r=n(2115),o=n(6101),i=n(2712),a=e=>{let{present:t,children:n}=e,a=function(e){var t,n;let[o,a]=r.useState(),u=r.useRef(null),s=r.useRef(e),c=r.useRef("none"),[f,d]=(t=e?"mounted":"unmounted",n={mounted:{UNMOUNT:"unmounted",ANIMATION_OUT:"unmountSuspended"},unmountSuspended:{MOUNT:"mounted",ANIMATION_END:"unmounted"},unmounted:{MOUNT:"mounted"}},r.useReducer((e,t)=>{let r=n[e][t];return null!=r?r:e},t));return r.useEffect(()=>{let e=l(u.current);c.current="mounted"===f?e:"none"},[f]),(0,i.N)(()=>{let t=u.current,n=s.current;if(n!==e){let r=c.current,o=l(t);e?d("MOUNT"):"none"===o||(null==t?void 0:t.display)==="none"?d("UNMOUNT"):n&&r!==o?d("ANIMATION_OUT"):d("UNMOUNT"),s.current=e}},[e,d]),(0,i.N)(()=>{if(o){var e;let t,n=null!=(e=o.ownerDocument.defaultView)?e:window,r=e=>{let r=l(u.current).includes(e.animationName);if(e.target===o&&r&&(d("ANIMATION_END"),!s.current)){let e=o.style.animationFillMode;o.style.animationFillMode="forwards",t=n.setTimeout(()=>{"forwards"===o.style.animationFillMode&&(o.style.animationFillMode=e)})}},i=e=>{e.target===o&&(c.current=l(u.current))};return o.addEventListener("animationstart",i),o.addEventListener("animationcancel",r),o.addEventListener("animationend",r),()=>{n.clearTimeout(t),o.removeEventListener("animationstart",i),o.removeEventListener("animationcancel",r),o.removeEventListener("animationend",r)}}d("ANIMATION_END")},[o,d]),{isPresent:["mounted","unmountSuspended"].includes(f),ref:r.useCallback(e=>{u.current=e?getComputedStyle(e):null,a(e)},[])}}(t),u="function"==typeof n?n({present:a.isPresent}):r.Children.only(n),s=(0,o.s)(a.ref,function(e){var t,n;let r=null==(t=Object.getOwnPropertyDescriptor(e.props,"ref"))?void 0:t.get,o=r&&"isReactWarning"in r&&r.isReactWarning;return o?e.ref:(o=(r=null==(n=Object.getOwnPropertyDescriptor(e,"ref"))?void 0:n.get)&&"isReactWarning"in r&&r.isReactWarning)?e.props.ref:e.props.ref||e.ref}(u));return"function"==typeof n||a.isPresent?r.cloneElement(u,{ref:s}):null};function l(e){return(null==e?void 0:e.animationName)||"none"}a.displayName="Presence"},9033:(e,t,n)=>{"use strict";n.d(t,{c:()=>o});var r=n(2115);function o(e){let t=r.useRef(e);return r.useEffect(()=>{t.current=e}),r.useMemo(()=>(...e)=>t.current?.(...e),[])}},9178:(e,t,n)=>{"use strict";n.d(t,{qW:()=>d});var r,o=n(2115),i=n(5185),a=n(3655),l=n(6101),u=n(9033),s=n(5155),c="dismissableLayer.update",f=o.createContext({layers:new Set,layersWithOutsidePointerEventsDisabled:new Set,branches:new Set}),d=o.forwardRef((e,t)=>{var n,d;let{disableOutsidePointerEvents:m=!1,onEscapeKeyDown:v,onPointerDownOutside:g,onFocusOutside:y,onInteractOutside:w,onDismiss:b,...x}=e,E=o.useContext(f),[R,C]=o.useState(null),A=null!=(d=null==R?void 0:R.ownerDocument)?d:null==(n=globalThis)?void 0:n.document,[,M]=o.useState({}),S=(0,l.s)(t,e=>C(e)),k=Array.from(E.layers),[O]=[...E.layersWithOutsidePointerEventsDisabled].slice(-1),P=k.indexOf(O),N=R?k.indexOf(R):-1,j=E.layersWithOutsidePointerEventsDisabled.size>0,T=N>=P,D=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1),a=o.useRef(()=>{});return o.useEffect(()=>{let e=e=>{if(e.target&&!i.current){let t=function(){h("dismissableLayer.pointerDownOutside",r,o,{discrete:!0})},o={originalEvent:e};"touch"===e.pointerType?(n.removeEventListener("click",a.current),a.current=t,n.addEventListener("click",a.current,{once:!0})):t()}else n.removeEventListener("click",a.current);i.current=!1},t=window.setTimeout(()=>{n.addEventListener("pointerdown",e)},0);return()=>{window.clearTimeout(t),n.removeEventListener("pointerdown",e),n.removeEventListener("click",a.current)}},[n,r]),{onPointerDownCapture:()=>i.current=!0}}(e=>{let t=e.target,n=[...E.branches].some(e=>e.contains(t));T&&!n&&(null==g||g(e),null==w||w(e),e.defaultPrevented||null==b||b())},A),L=function(e){var t;let n=arguments.length>1&&void 0!==arguments[1]?arguments[1]:null==(t=globalThis)?void 0:t.document,r=(0,u.c)(e),i=o.useRef(!1);return o.useEffect(()=>{let e=e=>{e.target&&!i.current&&h("dismissableLayer.focusOutside",r,{originalEvent:e},{discrete:!1})};return n.addEventListener("focusin",e),()=>n.removeEventListener("focusin",e)},[n,r]),{onFocusCapture:()=>i.current=!0,onBlurCapture:()=>i.current=!1}}(e=>{let t=e.target;![...E.branches].some(e=>e.contains(t))&&(null==y||y(e),null==w||w(e),e.defaultPrevented||null==b||b())},A);return!function(e,t=globalThis?.document){let n=(0,u.c)(e);o.useEffect(()=>{let e=e=>{"Escape"===e.key&&n(e)};return t.addEventListener("keydown",e,{capture:!0}),()=>t.removeEventListener("keydown",e,{capture:!0})},[n,t])}(e=>{N===E.layers.size-1&&(null==v||v(e),!e.defaultPrevented&&b&&(e.preventDefault(),b()))},A),o.useEffect(()=>{if(R)return m&&(0===E.layersWithOutsidePointerEventsDisabled.size&&(r=A.body.style.pointerEvents,A.body.style.pointerEvents="none"),E.layersWithOutsidePointerEventsDisabled.add(R)),E.layers.add(R),p(),()=>{m&&1===E.layersWithOutsidePointerEventsDisabled.size&&(A.body.style.pointerEvents=r)}},[R,A,m,E]),o.useEffect(()=>()=>{R&&(E.layers.delete(R),E.layersWithOutsidePointerEventsDisabled.delete(R),p())},[R,E]),o.useEffect(()=>{let e=()=>M({});return document.addEventListener(c,e),()=>document.removeEventListener(c,e)},[]),(0,s.jsx)(a.sG.div,{...x,ref:S,style:{pointerEvents:j?T?"auto":"none":void 0,...e.style},onFocusCapture:(0,i.m)(e.onFocusCapture,L.onFocusCapture),onBlurCapture:(0,i.m)(e.onBlurCapture,L.onBlurCapture),onPointerDownCapture:(0,i.m)(e.onPointerDownCapture,D.onPointerDownCapture)})});function p(){let e=new CustomEvent(c);document.dispatchEvent(e)}function h(e,t,n,r){let{discrete:o}=r,i=n.originalEvent.target,l=new CustomEvent(e,{bubbles:!1,cancelable:!0,detail:n});t&&i.addEventListener(e,t,{once:!0}),o?(0,a.hO)(i,l):i.dispatchEvent(l)}d.displayName="DismissableLayer",o.forwardRef((e,t)=>{let n=o.useContext(f),r=o.useRef(null),i=(0,l.s)(t,r);return o.useEffect(()=>{let e=r.current;if(e)return n.branches.add(e),()=>{n.branches.delete(e)}},[n.branches]),(0,s.jsx)(a.sG.div,{...e,ref:i})}).displayName="DismissableLayerBranch"},9991:(e,t)=>{"use strict";Object.defineProperty(t,"__esModule",{value:!0}),!function(e,t){for(var n in t)Object.defineProperty(e,n,{enumerable:!0,get:t[n]})}(t,{DecodeError:function(){return h},MiddlewareNotFoundError:function(){return y},MissingStaticPage:function(){return g},NormalizeError:function(){return m},PageNotFoundError:function(){return v},SP:function(){return d},ST:function(){return p},WEB_VITALS:function(){return n},execOnce:function(){return r},getDisplayName:function(){return u},getLocationOrigin:function(){return a},getURL:function(){return l},isAbsoluteUrl:function(){return i},isResSent:function(){return s},loadGetInitialProps:function(){return f},normalizeRepeatedSlashes:function(){return c},stringifyError:function(){return w}});let n=["CLS","FCP","FID","INP","LCP","TTFB"];function r(e){let t,n=!1;return function(){for(var r=arguments.length,o=Array(r),i=0;i<r;i++)o[i]=arguments[i];return n||(n=!0,t=e(...o)),t}}let o=/^[a-zA-Z][a-zA-Z\d+\-.]*?:/,i=e=>o.test(e);function a(){let{protocol:e,hostname:t,port:n}=window.location;return e+"//"+t+(n?":"+n:"")}function l(){let{href:e}=window.location,t=a();return e.substring(t.length)}function u(e){return"string"==typeof e?e:e.displayName||e.name||"Unknown"}function s(e){return e.finished||e.headersSent}function c(e){let t=e.split("?");return t[0].replace(/\\/g,"/").replace(/\/\/+/g,"/")+(t[1]?"?"+t.slice(1).join("?"):"")}async function f(e,t){let n=t.res||t.ctx&&t.ctx.res;if(!e.getInitialProps)return t.ctx&&t.Component?{pageProps:await f(t.Component,t.ctx)}:{};let r=await e.getInitialProps(t);if(n&&s(n))return r;if(!r)throw Object.defineProperty(Error('"'+u(e)+'.getInitialProps()" should resolve to an object. But found "'+r+'" instead.'),"__NEXT_ERROR_CODE",{value:"E394",enumerable:!1,configurable:!0});return r}let d="undefined"!=typeof performance,p=d&&["mark","measure","getEntriesByName"].every(e=>"function"==typeof performance[e]);class h extends Error{}class m extends Error{}class v extends Error{constructor(e){super(),this.code="ENOENT",this.name="PageNotFoundError",this.message="Cannot find module for page: "+e}}class g extends Error{constructor(e,t){super(),this.message="Failed to load static file for page: "+e+" "+t}}class y extends Error{constructor(){super(),this.code="ENOENT",this.message="Cannot find the middleware module"}}function w(e){return JSON.stringify({message:e.message,stack:e.stack})}}}]);