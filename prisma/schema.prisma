// This is your Prisma schema file,
// learn more about it in the docs: https://pris.ly/d/prisma-schema

// Looking for ways to speed up your queries, or scale easily with your serverless or edge functions?
// Try Prisma Accelerate: https://pris.ly/cli/accelerate-init

generator client {
  provider = "prisma-client-js"
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

// Core entities
model Actor {
  actorId    Int      @id @map("actor_id")
  firstName  String   @map("first_name") @db.VarChar(45)
  lastName   String   @map("last_name") @db.VarChar(45)
  lastUpdate DateTime @default(now()) @map("last_update") @db.Timestamp(6)

  filmActors FilmActor[]

  @@map("actor")
}

model Category {
  categoryId Int      @id @map("category_id")
  name       String   @db.VarChar(25)
  lastUpdate DateTime @default(now()) @map("last_update") @db.Timestamp(6)

  filmCategories FilmCategory[]

  @@map("category")
}

model Country {
  countryId  Int      @id @map("country_id")
  country    String   @db.VarChar(50)
  lastUpdate DateTime @default(now()) @map("last_update") @db.Timestamp(6)

  cities City[]

  @@map("country")
}

model City {
  cityId     Int      @id @map("city_id")
  city       String   @db.VarChar(50)
  countryId  Int      @map("country_id")
  lastUpdate DateTime @default(now()) @map("last_update") @db.Timestamp(6)

  country   Country   @relation(fields: [countryId], references: [countryId])
  addresses Address[]

  @@map("city")
}

model Address {
  addressId  Int      @id @map("address_id")
  address    String   @db.VarChar(50)
  address2   String?  @map("address2") @db.VarChar(50)
  district   String   @db.VarChar(20)
  cityId     Int      @map("city_id")
  postalCode String?  @map("postal_code") @db.VarChar(10)
  phone      String   @db.VarChar(20)
  lastUpdate DateTime @default(now()) @map("last_update") @db.Timestamp(6)

  city      City       @relation(fields: [cityId], references: [cityId])
  customers Customer[]
  staff     Staff[]

  @@map("address")
}

model Language {
  languageId Int      @id @map("language_id")
  name       String   @db.Char(20)
  lastUpdate DateTime @default(now()) @map("last_update") @db.Timestamp(6)

  films Film[]

  @@map("language")
}

model Film {
  filmId          Int      @id @map("film_id")
  title           String   @db.VarChar(255)
  description     String?  @db.Text
  releaseYear     Int?     @map("release_year")
  languageId      Int      @map("language_id")
  rentalDuration  Int      @default(3) @map("rental_duration") @db.SmallInt
  rentalRate      Decimal  @default(4.99) @map("rental_rate") @db.Decimal(4, 2)
  length          Int?     @db.SmallInt
  replacementCost Decimal  @default(19.99) @map("replacement_cost") @db.Decimal(5, 2)
  rating          String?  @default("G") @db.VarChar(10)
  lastUpdate      DateTime @default(now()) @map("last_update") @db.Timestamp(6)
  specialFeatures String[] @map("special_features")

  language       Language       @relation(fields: [languageId], references: [languageId])
  filmActors     FilmActor[]
  filmCategories FilmCategory[]
  inventory      Inventory[]

  @@map("film")
}

model Customer {
  customerId Int      @id @map("customer_id")
  storeId    Int      @map("store_id") @db.SmallInt
  firstName  String   @map("first_name") @db.VarChar(45)
  lastName   String   @map("last_name") @db.VarChar(45)
  email      String?  @db.VarChar(50)
  addressId  Int      @map("address_id")
  activebool Boolean  @default(true)
  createDate DateTime @default(now()) @map("create_date") @db.Date
  lastUpdate DateTime @default(now()) @map("last_update") @db.Timestamp(6)
  active     Int?

  address Address   @relation(fields: [addressId], references: [addressId])
  rentals Rental[]
  payments Payment[]

  @@map("customer")
}

model Staff {
  staffId    Int      @id @map("staff_id")
  firstName  String   @map("first_name") @db.VarChar(45)
  lastName   String   @map("last_name") @db.VarChar(45)
  addressId  Int      @map("address_id")
  email      String?  @db.VarChar(50)
  storeId    Int      @map("store_id") @db.SmallInt
  active     Boolean  @default(true)
  username   String   @db.VarChar(16)
  password   String?  @db.VarChar(40)
  lastUpdate DateTime @default(now()) @map("last_update") @db.Timestamp(6)
  picture    Bytes?

  address  Address   @relation(fields: [addressId], references: [addressId])
  rentals  Rental[]
  payments Payment[]

  @@map("staff")
}
